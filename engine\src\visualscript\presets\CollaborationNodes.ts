/**
 * 视觉脚本实时协作节点
 * 提供多用户实时协作编辑功能
 */
import { AsyncNode } from '../nodes/AsyncNode';
import { FunctionNode } from '../nodes/FunctionNode';
import { NodeCategory, SocketDirection, SocketType } from '../nodes/Node';
import { NodeRegistry } from '../nodes/NodeRegistry';
import { ExecutionContext } from '../execution/ExecutionContext';

/**
 * 协作会话接口
 */
export interface CollaborationSession {
  id: string;
  name: string;
  owner: string;
  participants: string[];
  createdAt: number;
  lastActivity: number;
  permissions: Record<string, string[]>;
  metadata?: Record<string, any>;
}

/**
 * 协作操作接口
 */
export interface CollaborationOperation {
  id: string;
  type: 'insert' | 'delete' | 'update' | 'move' | 'select';
  userId: string;
  timestamp: number;
  data: any;
  position?: any;
  metadata?: Record<string, any>;
}

/**
 * 创建协作会话节点
 */
export class CreateCollaborationSessionNode extends AsyncNode {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    this.addInput({
      name: 'sessionName',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '会话名称',
      required: true
    });

    this.addInput({
      name: 'owner',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '会话所有者',
      required: true
    });

    this.addInput({
      name: 'maxParticipants',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '最大参与者数量',
      defaultValue: 10
    });

    this.addInput({
      name: 'permissions',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '权限配置',
      defaultValue: { default: ['read', 'write'] }
    });

    this.addInput({
      name: 'isPrivate',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '是否私有会话',
      defaultValue: false
    });

    this.addInput({
      name: 'autoSave',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '是否自动保存',
      defaultValue: true
    });

    this.addInput({
      name: 'saveInterval',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '自动保存间隔（毫秒）',
      defaultValue: 30000
    });

    // 输出插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '创建成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '创建失败'
    });

    this.addOutput({
      name: 'session',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '协作会话对象'
    });

    this.addOutput({
      name: 'sessionId',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '会话ID'
    });

    this.addOutput({
      name: 'joinUrl',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '加入会话的URL'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '错误信息'
    });
  }

  async execute(context: ExecutionContext): Promise<boolean> {
    const sessionName = this.getInputValue('sessionName') as string;
    const owner = this.getInputValue('owner') as string;
    const maxParticipants = this.getInputValue('maxParticipants') as number;
    const permissions = this.getInputValue('permissions') as any;
    const isPrivate = this.getInputValue('isPrivate') as boolean;
    const autoSave = this.getInputValue('autoSave') as boolean;
    const saveInterval = this.getInputValue('saveInterval') as number;

    if (!sessionName || !owner) {
      this.setOutputValue('error', '会话名称和所有者不能为空');
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 获取协作服务
      const collaborationService = context.world?.getService('Collaboration');
      if (!collaborationService) {
        this.setOutputValue('error', '协作服务未找到');
        this.triggerFlow('fail');
        return false;
      }

      // 创建协作会话
      const session = await (collaborationService as any).createSession({
        name: sessionName,
        owner,
        maxParticipants,
        permissions,
        isPrivate,
        autoSave,
        saveInterval
      });

      // 设置输出值
      this.setOutputValue('session', session);
      this.setOutputValue('sessionId', session.id);
      this.setOutputValue('joinUrl', session.joinUrl);

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('创建协作会话失败:', errorMessage);
      this.setOutputValue('error', errorMessage);
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 加入协作会话节点
 */
export class JoinCollaborationSessionNode extends AsyncNode {
  private session: any = null;

  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'join',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '加入会话'
    });

    this.addInput({
      name: 'leave',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '离开会话'
    });

    this.addInput({
      name: 'sessionId',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '会话ID',
      required: true
    });

    this.addInput({
      name: 'userId',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '用户ID',
      required: true
    });

    this.addInput({
      name: 'userName',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '用户名称',
      required: true
    });

    this.addInput({
      name: 'userRole',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '用户角色（owner, editor, viewer）',
      defaultValue: 'editor'
    });

    // 输出插槽
    this.addOutput({
      name: 'onJoin',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '加入成功'
    });

    this.addOutput({
      name: 'onLeave',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '离开会话'
    });

    this.addOutput({
      name: 'onUserJoin',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '其他用户加入'
    });

    this.addOutput({
      name: 'onUserLeave',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '其他用户离开'
    });

    this.addOutput({
      name: 'onOperation',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '协作操作'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '操作失败'
    });

    this.addOutput({
      name: 'sessionInfo',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '会话信息'
    });

    this.addOutput({
      name: 'participants',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.OUTPUT,
      description: '参与者列表'
    });

    this.addOutput({
      name: 'isConnected',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否已连接'
    });

    this.addOutput({
      name: 'newUser',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '新加入的用户'
    });

    this.addOutput({
      name: 'leftUser',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '离开的用户'
    });

    this.addOutput({
      name: 'operation',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '协作操作数据'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '错误信息'
    });
  }

  async execute(context: ExecutionContext): Promise<boolean> {
    const inputSocket = this.getTriggeredInputSocket();

    try {
      // 获取协作服务
      const collaborationService = context.world?.getService('Collaboration');
      if (!collaborationService) {
        this.setOutputValue('error', '协作服务未找到');
        this.triggerFlow('fail');
        return false;
      }

      if (inputSocket?.name === 'join') {
        return this.joinSession(collaborationService);
      } else if (inputSocket?.name === 'leave') {
        return this.leaveSession();
      }

      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('协作会话操作失败:', errorMessage);
      this.setOutputValue('error', errorMessage);
      this.triggerFlow('fail');
      return false;
    }
  }

  /**
   * 加入会话
   */
  private async joinSession(collaborationService: any): Promise<boolean> {
    const sessionId = this.getInputValue('sessionId') as string;
    const userId = this.getInputValue('userId') as string;
    const userName = this.getInputValue('userName') as string;
    const userRole = this.getInputValue('userRole') as string;

    if (!sessionId || !userId || !userName) {
      this.setOutputValue('error', '会话ID、用户ID和用户名称不能为空');
      this.triggerFlow('fail');
      return false;
    }

    this.session = await collaborationService.joinSession(sessionId, {
      userId,
      userName,
      userRole,
      onUserJoin: (user: any) => {
        this.setOutputValue('newUser', user);
        this.updateParticipants();
        this.triggerFlow('onUserJoin');
      },
      onUserLeave: (user: any) => {
        this.setOutputValue('leftUser', user);
        this.updateParticipants();
        this.triggerFlow('onUserLeave');
      },
      onOperation: (operation: CollaborationOperation) => {
        this.setOutputValue('operation', operation);
        this.triggerFlow('onOperation');
      }
    });

    // 设置输出值
    this.setOutputValue('sessionInfo', this.session.info);
    this.setOutputValue('isConnected', true);
    this.updateParticipants();

    // 触发加入成功事件
    this.triggerFlow('onJoin');
    return true;
  }

  /**
   * 离开会话
   */
  private async leaveSession(): Promise<boolean> {
    if (this.session) {
      await this.session.leave();
      this.session = null;
      this.setOutputValue('isConnected', false);
      this.setOutputValue('participants', []);
      this.triggerFlow('onLeave');
    }
    return true;
  }

  /**
   * 更新参与者列表
   */
  private updateParticipants(): void {
    if (this.session) {
      const participants = this.session.getParticipants();
      this.setOutputValue('participants', participants);
    }
  }

  /**
   * 节点销毁时清理会话
   */
  public destroy(): void {
    this.leaveSession();
    super.destroy();
  }
}

/**
 * 发送协作操作节点
 */
export class SendCollaborationOperationNode extends AsyncNode {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    this.addInput({
      name: 'sessionId',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '会话ID',
      required: true
    });

    this.addInput({
      name: 'operationType',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '操作类型（insert, delete, update, move, select）',
      required: true
    });

    this.addInput({
      name: 'operationData',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '操作数据',
      required: true
    });

    this.addInput({
      name: 'position',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '操作位置'
    });

    this.addInput({
      name: 'userId',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '用户ID',
      required: true
    });

    this.addInput({
      name: 'metadata',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '附加元数据',
      defaultValue: {}
    });

    // 输出插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '发送成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '发送失败'
    });

    this.addOutput({
      name: 'operationId',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '操作ID'
    });

    this.addOutput({
      name: 'timestamp',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '操作时间戳'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '错误信息'
    });
  }

  async execute(context: ExecutionContext): Promise<boolean> {
    const sessionId = this.getInputValue('sessionId') as string;
    const operationType = this.getInputValue('operationType') as string;
    const operationData = this.getInputValue('operationData') as any;
    const position = this.getInputValue('position') as any;
    const userId = this.getInputValue('userId') as string;
    const metadata = this.getInputValue('metadata') as any;

    if (!sessionId || !operationType || !operationData || !userId) {
      this.setOutputValue('error', '会话ID、操作类型、操作数据和用户ID不能为空');
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 获取协作服务
      const collaborationService = context.world?.getService('Collaboration');
      if (!collaborationService) {
        this.setOutputValue('error', '协作服务未找到');
        this.triggerFlow('fail');
        return false;
      }

      // 创建协作操作
      const operation: CollaborationOperation = {
        id: this.generateOperationId(),
        type: operationType as any,
        userId,
        timestamp: Date.now(),
        data: operationData,
        position,
        metadata
      };

      // 发送操作
      await (collaborationService as any).sendOperation(sessionId, operation);

      // 设置输出值
      this.setOutputValue('operationId', operation.id);
      this.setOutputValue('timestamp', operation.timestamp);

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('发送协作操作失败:', errorMessage);
      this.setOutputValue('error', errorMessage);
      this.triggerFlow('fail');
      return false;
    }
  }

  /**
   * 生成操作ID
   */
  private generateOperationId(): string {
    return `op_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

/**
 * 数据同步节点
 */
export class DataSynchronizationNode extends AsyncNode {
  private syncManager: any = null;

  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'start',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '开始同步'
    });

    this.addInput({
      name: 'stop',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '停止同步'
    });

    this.addInput({
      name: 'sync',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '手动同步'
    });

    this.addInput({
      name: 'sessionId',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '会话ID',
      required: true
    });

    this.addInput({
      name: 'dataSource',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '数据源',
      required: true
    });

    this.addInput({
      name: 'syncInterval',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '同步间隔（毫秒）',
      defaultValue: 5000
    });

    this.addInput({
      name: 'conflictResolution',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '冲突解决策略（last_write_wins, merge, manual）',
      defaultValue: 'last_write_wins'
    });

    this.addInput({
      name: 'enableCompression',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '是否启用压缩',
      defaultValue: true
    });

    // 输出插槽
    this.addOutput({
      name: 'onStart',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '同步开始'
    });

    this.addOutput({
      name: 'onStop',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '同步停止'
    });

    this.addOutput({
      name: 'onSync',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '同步完成'
    });

    this.addOutput({
      name: 'onConflict',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '冲突检测'
    });

    this.addOutput({
      name: 'syncStatus',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '同步状态'
    });

    this.addOutput({
      name: 'isSyncing',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否同步中'
    });

    this.addOutput({
      name: 'lastSyncTime',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '最后同步时间'
    });

    this.addOutput({
      name: 'conflictData',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '冲突数据'
    });

    this.addOutput({
      name: 'syncedData',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '同步后的数据'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '错误信息'
    });
  }

  async execute(context: ExecutionContext): Promise<boolean> {
    const inputSocket = this.getTriggeredInputSocket();

    try {
      // 获取协作服务
      const collaborationService = context.world?.getService('Collaboration');
      if (!collaborationService) {
        this.setOutputValue('error', '协作服务未找到');
        return false;
      }

      if (inputSocket?.name === 'start') {
        return this.startSync(collaborationService);
      } else if (inputSocket?.name === 'stop') {
        return this.stopSync();
      } else if (inputSocket?.name === 'sync') {
        return this.manualSync(collaborationService);
      }

      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('数据同步操作失败:', errorMessage);
      this.setOutputValue('error', errorMessage);
      return false;
    }
  }

  /**
   * 开始同步
   */
  private async startSync(collaborationService: any): Promise<boolean> {
    const sessionId = this.getInputValue('sessionId') as string;
    const dataSource = this.getInputValue('dataSource') as any;
    const syncInterval = this.getInputValue('syncInterval') as number;
    const conflictResolution = this.getInputValue('conflictResolution') as string;
    const enableCompression = this.getInputValue('enableCompression') as boolean;

    if (!sessionId || !dataSource) {
      this.setOutputValue('error', '会话ID和数据源不能为空');
      return false;
    }

    this.syncManager = await collaborationService.createSyncManager({
      sessionId,
      dataSource,
      syncInterval,
      conflictResolution,
      enableCompression,
      onSync: (syncedData: any) => {
        this.setOutputValue('syncedData', syncedData);
        this.setOutputValue('lastSyncTime', Date.now());
        this.updateSyncStatus();
        this.triggerFlow('onSync');
      },
      onConflict: (conflictData: any) => {
        this.setOutputValue('conflictData', conflictData);
        this.triggerFlow('onConflict');
      }
    });

    this.setOutputValue('isSyncing', true);
    this.updateSyncStatus();
    this.triggerFlow('onStart');

    return true;
  }

  /**
   * 停止同步
   */
  private async stopSync(): Promise<boolean> {
    if (this.syncManager) {
      await this.syncManager.stop();
      this.syncManager = null;
      this.setOutputValue('isSyncing', false);
      this.updateSyncStatus();
      this.triggerFlow('onStop');
    }
    return true;
  }

  /**
   * 手动同步
   */
  private async manualSync(collaborationService: any): Promise<boolean> {
    const sessionId = this.getInputValue('sessionId') as string;
    const dataSource = this.getInputValue('dataSource') as any;

    if (!sessionId || !dataSource) {
      this.setOutputValue('error', '会话ID和数据源不能为空');
      return false;
    }

    const syncedData = await collaborationService.syncData(sessionId, dataSource);

    this.setOutputValue('syncedData', syncedData);
    this.setOutputValue('lastSyncTime', Date.now());
    this.triggerFlow('onSync');

    return true;
  }

  /**
   * 更新同步状态
   */
  private updateSyncStatus(): void {
    if (this.syncManager) {
      const status = this.syncManager.getStatus();
      this.setOutputValue('syncStatus', status);
    }
  }

  /**
   * 节点销毁时清理同步管理器
   */
  public destroy(): void {
    this.stopSync();
    super.destroy();
  }
}

/**
 * 注册实时协作节点
 * @param registry 节点注册表
 */
export function registerCollaborationNodes(registry: NodeRegistry): void {
  // 注册创建协作会话节点
  registry.registerNodeType({
    type: 'collaboration/create-session',
    category: NodeCategory.NETWORK,
    constructor: CreateCollaborationSessionNode,
    label: '创建协作会话',
    description: '创建多用户实时协作编辑会话',
    icon: 'team',
    color: '#13c2c2',
    tags: ['collaboration', 'session', 'realtime', 'multiplayer']
  });

  // 注册加入协作会话节点
  registry.registerNodeType({
    type: 'collaboration/join-session',
    category: NodeCategory.NETWORK,
    constructor: JoinCollaborationSessionNode,
    label: '加入协作会话',
    description: '加入现有的协作编辑会话',
    icon: 'user-add',
    color: '#13c2c2',
    tags: ['collaboration', 'join', 'realtime', 'multiplayer']
  });

  // 注册发送协作操作节点
  registry.registerNodeType({
    type: 'collaboration/send-operation',
    category: NodeCategory.NETWORK,
    constructor: SendCollaborationOperationNode,
    label: '发送协作操作',
    description: '向协作会话发送编辑操作',
    icon: 'send',
    color: '#13c2c2',
    tags: ['collaboration', 'operation', 'edit', 'sync']
  });

  // 注册数据同步节点
  registry.registerNodeType({
    type: 'collaboration/data-sync',
    category: NodeCategory.NETWORK,
    constructor: DataSynchronizationNode,
    label: '数据同步',
    description: '管理协作会话中的数据同步',
    icon: 'sync',
    color: '#13c2c2',
    tags: ['collaboration', 'sync', 'data', 'conflict']
  });

  console.log('已注册所有实时协作节点类型');
}
