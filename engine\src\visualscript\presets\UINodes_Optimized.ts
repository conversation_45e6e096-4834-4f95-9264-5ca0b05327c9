/**
 * UINodes_Optimized.ts
 * 
 * 优化后的UI相关视觉脚本节点 - 使用统一插槽系统
 */

import { FlowNode } from '../nodes/FlowNode';
import { NodeCategory, SocketType, SocketDirection } from '../nodes/Node';
import { NodeRegistry } from '../nodes/NodeRegistry';
import { ExecutionContext } from '../execution/ExecutionContext';

/**
 * 创建输入框节点
 */
export class CreateInputNode extends FlowNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'placeholder',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '占位符',
      defaultValue: '请输入...'
    });

    this.addInput({
      name: 'position',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'object',
      description: '位置',
      defaultValue: { x: 0, y: 0 }
    });

    this.addInput({
      name: 'size',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'object',
      description: '大小',
      defaultValue: { x: 200, y: 30 }
    });

    this.addInput({
      name: 'inputType',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '输入类型',
      defaultValue: 'text'
    });

    this.addInput({
      name: 'style',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'object',
      description: '样式',
      defaultValue: {}
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '创建成功'
    });

    this.addOutput({
      name: 'onChange',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '值改变'
    });

    this.addOutput({
      name: 'onFocus',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '获得焦点'
    });

    this.addOutput({
      name: 'onBlur',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '失去焦点'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'inputElement',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'object',
      description: '输入框元素'
    });

    this.addOutput({
      name: 'value',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'string',
      description: '当前值'
    });
  }

  /**
   * 执行节点
   */
  public execute(): any {
    const placeholder = this.getInputValue('placeholder') as string;
    const position = this.getInputValue('position') as { x: number; y: number };
    const size = this.getInputValue('size') as { x: number; y: number };
    const inputType = this.getInputValue('inputType') as string;
    const style = this.getInputValue('style') as any;

    try {
      // 获取UI系统
      const uiSystem = this.executionContext?.world?.getSystem('UISystem');
      if (!uiSystem) {
        console.error('UI系统未找到');
        return false;
      }

      // 创建输入框
      const inputElement = (uiSystem as any).createInput({
        placeholder,
        position,
        size,
        type: inputType,
        style,
        onChange: (value: string) => {
          this.setOutputValue('value', value);
          this.triggerFlow('onChange');
        },
        onFocus: () => {
          this.triggerFlow('onFocus');
        },
        onBlur: () => {
          this.triggerFlow('onBlur');
        }
      });

      // 设置输出值
      this.setOutputValue('inputElement', inputElement);

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      console.error('创建输入框失败:', error);
      return false;
    }
  }
}

/**
 * 创建滑块节点
 */
export class CreateSliderNode extends FlowNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'min',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '最小值',
      defaultValue: 0
    });

    this.addInput({
      name: 'max',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '最大值',
      defaultValue: 100
    });

    this.addInput({
      name: 'value',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '初始值',
      defaultValue: 50
    });

    this.addInput({
      name: 'step',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '步长',
      defaultValue: 1
    });

    this.addInput({
      name: 'position',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'object',
      description: '位置',
      defaultValue: { x: 0, y: 0 }
    });

    this.addInput({
      name: 'size',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'object',
      description: '大小',
      defaultValue: { x: 200, y: 20 }
    });

    this.addInput({
      name: 'style',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'object',
      description: '样式',
      defaultValue: {}
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '创建成功'
    });

    this.addOutput({
      name: 'onChange',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '值改变'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'sliderElement',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'object',
      description: '滑块元素'
    });

    this.addOutput({
      name: 'currentValue',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '当前值'
    });
  }

  /**
   * 执行节点
   */
  public execute(): any {
    const min = this.getInputValue('min') as number;
    const max = this.getInputValue('max') as number;
    const value = this.getInputValue('value') as number;
    const step = this.getInputValue('step') as number;
    const position = this.getInputValue('position') as { x: number; y: number };
    const size = this.getInputValue('size') as { x: number; y: number };
    const style = this.getInputValue('style') as any;

    try {
      // 获取UI系统
      const uiSystem = this.executionContext?.world?.getSystem('UISystem');
      if (!uiSystem) {
        console.error('UI系统未找到');
        return false;
      }

      // 创建滑块
      const sliderElement = (uiSystem as any).createSlider({
        min,
        max,
        value,
        step,
        position,
        size,
        style,
        onChange: (newValue: number) => {
          this.setOutputValue('currentValue', newValue);
          this.triggerFlow('onChange');
        }
      });

      // 设置输出值
      this.setOutputValue('sliderElement', sliderElement);
      this.setOutputValue('currentValue', value);

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      console.error('创建滑块失败:', error);
      return false;
    }
  }
}

/**
 * 创建下拉选择框节点
 */
export class CreateSelectNode extends FlowNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'options',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'array',
      description: '选项列表',
      defaultValue: []
    });

    this.addInput({
      name: 'placeholder',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '占位符',
      defaultValue: '请选择...'
    });

    this.addInput({
      name: 'position',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'object',
      description: '位置',
      defaultValue: { x: 0, y: 0 }
    });

    this.addInput({
      name: 'size',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'object',
      description: '大小',
      defaultValue: { x: 200, y: 30 }
    });

    this.addInput({
      name: 'multiple',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'boolean',
      description: '多选',
      defaultValue: false
    });

    this.addInput({
      name: 'style',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'object',
      description: '样式',
      defaultValue: {}
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '创建成功'
    });

    this.addOutput({
      name: 'onChange',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '选择改变'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'selectElement',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'object',
      description: '选择框元素'
    });

    this.addOutput({
      name: 'selectedValue',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'any',
      description: '选中值'
    });

    this.addOutput({
      name: 'selectedValues',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'array',
      description: '选中值列表'
    });
  }

  /**
   * 执行节点
   */
  public execute(): any {
    const options = this.getInputValue('options') as any[];
    const placeholder = this.getInputValue('placeholder') as string;
    const position = this.getInputValue('position') as { x: number; y: number };
    const size = this.getInputValue('size') as { x: number; y: number };
    const multiple = this.getInputValue('multiple') as boolean;
    const style = this.getInputValue('style') as any;

    try {
      // 获取UI系统
      const uiSystem = this.executionContext?.world?.getSystem('UISystem');
      if (!uiSystem) {
        console.error('UI系统未找到');
        return false;
      }

      // 创建选择框
      const selectElement = (uiSystem as any).createSelect({
        options,
        placeholder,
        position,
        size,
        multiple,
        style,
        onChange: (value: any, values: any[]) => {
          this.setOutputValue('selectedValue', value);
          this.setOutputValue('selectedValues', values);
          this.triggerFlow('onChange');
        }
      });

      // 设置输出值
      this.setOutputValue('selectElement', selectElement);

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      console.error('创建选择框失败:', error);
      return false;
    }
  }
}

/**
 * 创建复选框节点
 */
export class CreateCheckboxNode extends FlowNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'label',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '标签',
      defaultValue: '复选框'
    });

    this.addInput({
      name: 'checked',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'boolean',
      description: '初始选中',
      defaultValue: false
    });

    this.addInput({
      name: 'position',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'object',
      description: '位置',
      defaultValue: { x: 0, y: 0 }
    });

    this.addInput({
      name: 'disabled',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'boolean',
      description: '禁用',
      defaultValue: false
    });

    this.addInput({
      name: 'style',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'object',
      description: '样式',
      defaultValue: {}
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '创建成功'
    });

    this.addOutput({
      name: 'onChange',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '状态改变'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'checkboxElement',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'object',
      description: '复选框元素'
    });

    this.addOutput({
      name: 'isChecked',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'boolean',
      description: '是否选中'
    });
  }

  /**
   * 执行节点
   */
  public execute(): any {
    const label = this.getInputValue('label') as string;
    const checked = this.getInputValue('checked') as boolean;
    const position = this.getInputValue('position') as { x: number; y: number };
    const disabled = this.getInputValue('disabled') as boolean;
    const style = this.getInputValue('style') as any;

    try {
      // 获取UI系统
      const uiSystem = this.executionContext?.world?.getSystem('UISystem');
      if (!uiSystem) {
        console.error('UI系统未找到');
        return false;
      }

      // 创建复选框
      const checkboxElement = (uiSystem as any).createCheckbox({
        label,
        checked,
        position,
        disabled,
        style,
        onChange: (isChecked: boolean) => {
          this.setOutputValue('isChecked', isChecked);
          this.triggerFlow('onChange');
        }
      });

      // 设置输出值
      this.setOutputValue('checkboxElement', checkboxElement);
      this.setOutputValue('isChecked', checked);

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      console.error('创建复选框失败:', error);
      return false;
    }
  }
}

/**
 * 注册优化的UI节点
 */
export function registerOptimizedUINodes(registry: NodeRegistry): void {
  // 注册创建按钮节点
  registry.registerNodeType({
    type: 'ui/button/create',
    category: NodeCategory.UI,
    constructor: CreateButtonNode,
    label: '创建按钮',
    description: '创建一个可点击的按钮',
    icon: 'button',
    color: '#1890ff',
    tags: ['ui', 'button', 'create', 'interactive']
  });

  // 注册创建文本节点
  registry.registerNodeType({
    type: 'ui/text/create',
    category: NodeCategory.UI,
    constructor: CreateTextNode,
    label: '创建文本',
    description: '创建一个文本显示元素',
    icon: 'text',
    color: '#1890ff',
    tags: ['ui', 'text', 'create', 'display']
  });

  // 注册创建输入框节点
  registry.registerNodeType({
    type: 'ui/input/create',
    category: NodeCategory.UI,
    constructor: CreateInputNode,
    label: '创建输入框',
    description: '创建一个文本输入框',
    icon: 'input',
    color: '#1890ff',
    tags: ['ui', 'input', 'create', 'form']
  });

  // 注册创建滑块节点
  registry.registerNodeType({
    type: 'ui/slider/create',
    category: NodeCategory.UI,
    constructor: CreateSliderNode,
    label: '创建滑块',
    description: '创建一个数值滑块控件',
    icon: 'slider',
    color: '#1890ff',
    tags: ['ui', 'slider', 'create', 'input']
  });

  // 注册创建选择框节点
  registry.registerNodeType({
    type: 'ui/select/create',
    category: NodeCategory.UI,
    constructor: CreateSelectNode,
    label: '创建选择框',
    description: '创建一个下拉选择框',
    icon: 'select',
    color: '#1890ff',
    tags: ['ui', 'select', 'create', 'dropdown']
  });

  // 注册创建复选框节点
  registry.registerNodeType({
    type: 'ui/checkbox/create',
    category: NodeCategory.UI,
    constructor: CreateCheckboxNode,
    label: '创建复选框',
    description: '创建一个复选框控件',
    icon: 'checkbox',
    color: '#1890ff',
    tags: ['ui', 'checkbox', 'create', 'form']
  });
}
