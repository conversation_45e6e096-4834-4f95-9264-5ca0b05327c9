/**
 * FileSystemNodes_Optimized.ts
 * 
 * 优化后的文件系统相关视觉脚本节点 - 使用统一插槽系统
 */

import { FlowNode } from '../nodes/FlowNode';
import { AsyncNode } from '../nodes/AsyncNode';
import { NodeCategory, SocketType, SocketDirection } from '../nodes/Node';
import { NodeRegistry } from '../nodes/NodeRegistry';
import { ExecutionContext } from '../execution/ExecutionContext';

/**
 * 读取文本文件节点
 */
export class ReadTextFileNode extends AsyncNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'filePath',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '文件路径',
      defaultValue: ''
    });

    this.addInput({
      name: 'encoding',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '编码',
      defaultValue: 'utf-8'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '读取成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '读取失败'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'content',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'string',
      description: '文件内容'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'string',
      description: '错误信息'
    });
  }

  /**
   * 执行节点
   */
  public async execute(): Promise<any> {
    const filePath = this.getInputValue('filePath') as string;
    const encoding = this.getInputValue('encoding') as string;

    try {
      // 获取文件系统服务
      const fileSystem = this.executionContext?.world?.getService('FileSystem');
      if (!fileSystem) {
        const error = '文件系统服务未找到';
        this.setOutputValue('error', error);
        this.triggerFlow('fail');
        return false;
      }

      // 读取文件
      const content = await (fileSystem as any).readTextFile(filePath, encoding);
      
      // 设置输出值
      this.setOutputValue('content', content);

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('读取文件失败:', errorMessage);
      this.setOutputValue('error', errorMessage);
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 写入文本文件节点
 */
export class WriteTextFileNode extends AsyncNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'filePath',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '文件路径',
      defaultValue: ''
    });

    this.addInput({
      name: 'content',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '文件内容',
      defaultValue: ''
    });

    this.addInput({
      name: 'encoding',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '编码',
      defaultValue: 'utf-8'
    });

    this.addInput({
      name: 'append',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'boolean',
      description: '追加模式',
      defaultValue: false
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '写入成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '写入失败'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'string',
      description: '错误信息'
    });
  }

  /**
   * 执行节点
   */
  public async execute(): Promise<any> {
    const filePath = this.getInputValue('filePath') as string;
    const content = this.getInputValue('content') as string;
    const encoding = this.getInputValue('encoding') as string;
    const append = this.getInputValue('append') as boolean;

    try {
      // 获取文件系统服务
      const fileSystem = this.executionContext?.world?.getService('FileSystem');
      if (!fileSystem) {
        const error = '文件系统服务未找到';
        this.setOutputValue('error', error);
        this.triggerFlow('fail');
        return false;
      }

      // 写入文件
      await (fileSystem as any).writeTextFile(filePath, content, { encoding, append });

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('写入文件失败:', errorMessage);
      this.setOutputValue('error', errorMessage);
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 检查文件是否存在节点
 */
export class FileExistsNode extends AsyncNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'filePath',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '文件路径',
      defaultValue: ''
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'exists',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '文件存在'
    });

    this.addOutput({
      name: 'notExists',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '文件不存在'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'boolean',
      description: '检查结果'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'string',
      description: '错误信息'
    });
  }

  /**
   * 执行节点
   */
  public async execute(): Promise<any> {
    const filePath = this.getInputValue('filePath') as string;

    try {
      // 获取文件系统服务
      const fileSystem = this.executionContext?.world?.getService('FileSystem');
      if (!fileSystem) {
        const error = '文件系统服务未找到';
        this.setOutputValue('error', error);
        return false;
      }

      // 检查文件是否存在
      const exists = await (fileSystem as any).exists(filePath);
      
      // 设置输出值
      this.setOutputValue('result', exists);

      // 触发相应流程
      if (exists) {
        this.triggerFlow('exists');
      } else {
        this.triggerFlow('notExists');
      }
      
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('检查文件存在失败:', errorMessage);
      this.setOutputValue('error', errorMessage);
      return false;
    }
  }
}

/**
 * 创建目录节点
 */
export class CreateDirectoryNode extends AsyncNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'directoryPath',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '目录路径',
      defaultValue: ''
    });

    this.addInput({
      name: 'recursive',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'boolean',
      description: '递归创建',
      defaultValue: true
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '创建成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '创建失败'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'string',
      description: '错误信息'
    });
  }

  /**
   * 执行节点
   */
  public async execute(): Promise<any> {
    const directoryPath = this.getInputValue('directoryPath') as string;
    const recursive = this.getInputValue('recursive') as boolean;

    try {
      // 获取文件系统服务
      const fileSystem = this.executionContext?.world?.getService('FileSystem');
      if (!fileSystem) {
        const error = '文件系统服务未找到';
        this.setOutputValue('error', error);
        this.triggerFlow('fail');
        return false;
      }

      // 创建目录
      await (fileSystem as any).createDirectory(directoryPath, { recursive });

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('创建目录失败:', errorMessage);
      this.setOutputValue('error', errorMessage);
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 删除文件节点
 */
export class DeleteFileNode extends AsyncNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'filePath',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '文件路径',
      defaultValue: ''
    });

    this.addInput({
      name: 'force',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'boolean',
      description: '强制删除',
      defaultValue: false
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '删除成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '删除失败'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'string',
      description: '错误信息'
    });
  }

  /**
   * 执行节点
   */
  public async execute(): Promise<any> {
    const filePath = this.getInputValue('filePath') as string;
    const force = this.getInputValue('force') as boolean;

    try {
      // 获取文件系统服务
      const fileSystem = this.executionContext?.world?.getService('FileSystem');
      if (!fileSystem) {
        const error = '文件系统服务未找到';
        this.setOutputValue('error', error);
        this.triggerFlow('fail');
        return false;
      }

      // 删除文件
      await (fileSystem as any).deleteFile(filePath, { force });

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('删除文件失败:', errorMessage);
      this.setOutputValue('error', errorMessage);
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 注册优化的文件系统节点
 */
export function registerOptimizedFileSystemNodes(registry: NodeRegistry): void {
  // 注册读取文本文件节点
  registry.registerNodeType({
    type: 'file/read/text',
    category: NodeCategory.FILE,
    constructor: ReadTextFileNode,
    label: '读取文本文件',
    description: '读取文本文件内容',
    icon: 'file-text',
    color: '#52c41a',
    tags: ['file', 'read', 'text', 'io']
  });

  // 注册写入文本文件节点
  registry.registerNodeType({
    type: 'file/write/text',
    category: NodeCategory.FILE,
    constructor: WriteTextFileNode,
    label: '写入文本文件',
    description: '写入文本内容到文件',
    icon: 'file-text',
    color: '#52c41a',
    tags: ['file', 'write', 'text', 'io']
  });

  // 注册检查文件存在节点
  registry.registerNodeType({
    type: 'file/exists',
    category: NodeCategory.FILE,
    constructor: FileExistsNode,
    label: '检查文件存在',
    description: '检查文件或目录是否存在',
    icon: 'file-search',
    color: '#52c41a',
    tags: ['file', 'exists', 'check', 'io']
  });

  // 注册创建目录节点
  registry.registerNodeType({
    type: 'file/directory/create',
    category: NodeCategory.FILE,
    constructor: CreateDirectoryNode,
    label: '创建目录',
    description: '创建新目录',
    icon: 'folder-add',
    color: '#52c41a',
    tags: ['file', 'directory', 'create', 'io']
  });

  // 注册删除文件节点
  registry.registerNodeType({
    type: 'file/delete',
    category: NodeCategory.FILE,
    constructor: DeleteFileNode,
    label: '删除文件',
    description: '删除指定文件',
    icon: 'delete',
    color: '#ff4d4f',
    tags: ['file', 'delete', 'remove', 'io']
  });
}
