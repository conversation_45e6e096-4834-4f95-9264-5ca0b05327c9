/**
 * 视觉脚本加密解密节点
 * 提供完整的加密解密和哈希功能
 */
import { FunctionNode } from '../nodes/FunctionNode';
import { AsyncNode } from '../nodes/AsyncNode';
import { NodeCategory, SocketDirection, SocketType } from '../nodes/Node';
import { NodeRegistry } from '../nodes/NodeRegistry';
import { ExecutionContext } from '../execution/ExecutionContext';

/**
 * MD5哈希节点
 */
export class MD5HashNode extends FunctionNode {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'data',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '要哈希的数据',
      required: true
    });

    this.addInput({
      name: 'encoding',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '输入编码（utf8, base64, hex）',
      defaultValue: 'utf8'
    });

    this.addInput({
      name: 'outputFormat',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '输出格式（hex, base64）',
      defaultValue: 'hex'
    });

    // 输出插槽
    this.addOutput({
      name: 'hash',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: 'MD5哈希值'
    });

    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否成功'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '错误信息'
    });
  }

  execute(context: ExecutionContext): boolean {
    const data = this.getInputValue('data') as string;
    const encoding = this.getInputValue('encoding') as string;
    const outputFormat = this.getInputValue('outputFormat') as string;

    if (!data) {
      this.setOutputValue('hash', '');
      this.setOutputValue('success', false);
      this.setOutputValue('error', '数据不能为空');
      return false;
    }

    try {
      // 获取加密服务
      const cryptoService = context.world?.getService('Cryptography');
      if (!cryptoService) {
        this.setOutputValue('hash', '');
        this.setOutputValue('success', false);
        this.setOutputValue('error', '加密服务未找到');
        return false;
      }

      // 计算MD5哈希
      const hash = (cryptoService as any).md5(data, { encoding, outputFormat });

      this.setOutputValue('hash', hash);
      this.setOutputValue('success', true);
      this.setOutputValue('error', '');
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('MD5哈希计算失败:', errorMessage);
      this.setOutputValue('hash', '');
      this.setOutputValue('success', false);
      this.setOutputValue('error', errorMessage);
      return false;
    }
  }
}

/**
 * SHA256哈希节点
 */
export class SHA256HashNode extends FunctionNode {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'data',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '要哈希的数据',
      required: true
    });

    this.addInput({
      name: 'encoding',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '输入编码（utf8, base64, hex）',
      defaultValue: 'utf8'
    });

    this.addInput({
      name: 'outputFormat',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '输出格式（hex, base64）',
      defaultValue: 'hex'
    });

    this.addInput({
      name: 'salt',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '盐值（可选）'
    });

    // 输出插槽
    this.addOutput({
      name: 'hash',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: 'SHA256哈希值'
    });

    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否成功'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '错误信息'
    });
  }

  execute(context: ExecutionContext): boolean {
    const data = this.getInputValue('data') as string;
    const encoding = this.getInputValue('encoding') as string;
    const outputFormat = this.getInputValue('outputFormat') as string;
    const salt = this.getInputValue('salt') as string;

    if (!data) {
      this.setOutputValue('hash', '');
      this.setOutputValue('success', false);
      this.setOutputValue('error', '数据不能为空');
      return false;
    }

    try {
      // 获取加密服务
      const cryptoService = context.world?.getService('Cryptography');
      if (!cryptoService) {
        this.setOutputValue('hash', '');
        this.setOutputValue('success', false);
        this.setOutputValue('error', '加密服务未找到');
        return false;
      }

      // 计算SHA256哈希
      const hash = (cryptoService as any).sha256(data, { encoding, outputFormat, salt });

      this.setOutputValue('hash', hash);
      this.setOutputValue('success', true);
      this.setOutputValue('error', '');
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('SHA256哈希计算失败:', errorMessage);
      this.setOutputValue('hash', '');
      this.setOutputValue('success', false);
      this.setOutputValue('error', errorMessage);
      return false;
    }
  }
}

/**
 * AES加密节点
 */
export class AESEncryptNode extends AsyncNode {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    this.addInput({
      name: 'data',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '要加密的数据',
      required: true
    });

    this.addInput({
      name: 'key',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '加密密钥',
      required: true
    });

    this.addInput({
      name: 'iv',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '初始化向量（可选，自动生成）'
    });

    this.addInput({
      name: 'mode',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '加密模式（CBC, ECB, CFB, OFB, CTR）',
      defaultValue: 'CBC'
    });

    this.addInput({
      name: 'padding',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '填充方式（PKCS7, ZERO, NONE）',
      defaultValue: 'PKCS7'
    });

    this.addInput({
      name: 'outputFormat',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '输出格式（base64, hex）',
      defaultValue: 'base64'
    });

    // 输出插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '加密成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '加密失败'
    });

    this.addOutput({
      name: 'encrypted',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '加密后的数据'
    });

    this.addOutput({
      name: 'iv',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '使用的初始化向量'
    });

    this.addOutput({
      name: 'keySize',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '密钥长度（位）'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '错误信息'
    });
  }

  async execute(context: ExecutionContext): Promise<boolean> {
    const data = this.getInputValue('data') as string;
    const key = this.getInputValue('key') as string;
    const iv = this.getInputValue('iv') as string;
    const mode = this.getInputValue('mode') as string;
    const padding = this.getInputValue('padding') as string;
    const outputFormat = this.getInputValue('outputFormat') as string;

    if (!data || !key) {
      this.setOutputValue('error', '数据和密钥不能为空');
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 获取加密服务
      const cryptoService = context.world?.getService('Cryptography');
      if (!cryptoService) {
        this.setOutputValue('error', '加密服务未找到');
        this.triggerFlow('fail');
        return false;
      }

      // AES加密
      const result = await (cryptoService as any).aesEncrypt(data, key, {
        iv,
        mode,
        padding,
        outputFormat
      });

      // 设置输出值
      this.setOutputValue('encrypted', result.encrypted);
      this.setOutputValue('iv', result.iv);
      this.setOutputValue('keySize', result.keySize);

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('AES加密失败:', errorMessage);
      this.setOutputValue('error', errorMessage);
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * AES解密节点
 */
export class AESDecryptNode extends AsyncNode {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    this.addInput({
      name: 'encrypted',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '加密的数据',
      required: true
    });

    this.addInput({
      name: 'key',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '解密密钥',
      required: true
    });

    this.addInput({
      name: 'iv',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '初始化向量',
      required: true
    });

    this.addInput({
      name: 'mode',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '解密模式（CBC, ECB, CFB, OFB, CTR）',
      defaultValue: 'CBC'
    });

    this.addInput({
      name: 'padding',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '填充方式（PKCS7, ZERO, NONE）',
      defaultValue: 'PKCS7'
    });

    this.addInput({
      name: 'inputFormat',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '输入格式（base64, hex）',
      defaultValue: 'base64'
    });

    // 输出插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '解密成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '解密失败'
    });

    this.addOutput({
      name: 'decrypted',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '解密后的数据'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '错误信息'
    });
  }

  async execute(context: ExecutionContext): Promise<boolean> {
    const encrypted = this.getInputValue('encrypted') as string;
    const key = this.getInputValue('key') as string;
    const iv = this.getInputValue('iv') as string;
    const mode = this.getInputValue('mode') as string;
    const padding = this.getInputValue('padding') as string;
    const inputFormat = this.getInputValue('inputFormat') as string;

    if (!encrypted || !key || !iv) {
      this.setOutputValue('error', '加密数据、密钥和初始化向量不能为空');
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 获取加密服务
      const cryptoService = context.world?.getService('Cryptography');
      if (!cryptoService) {
        this.setOutputValue('error', '加密服务未找到');
        this.triggerFlow('fail');
        return false;
      }

      // AES解密
      const decrypted = await (cryptoService as any).aesDecrypt(encrypted, key, iv, {
        mode,
        padding,
        inputFormat
      });

      // 设置输出值
      this.setOutputValue('decrypted', decrypted);

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('AES解密失败:', errorMessage);
      this.setOutputValue('error', errorMessage);
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * Base64编码节点
 */
export class Base64EncodeNode extends FunctionNode {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'data',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '要编码的数据',
      required: true
    });

    this.addInput({
      name: 'encoding',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '输入编码（utf8, binary）',
      defaultValue: 'utf8'
    });

    this.addInput({
      name: 'urlSafe',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '是否使用URL安全编码',
      defaultValue: false
    });

    // 输出插槽
    this.addOutput({
      name: 'encoded',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: 'Base64编码后的数据'
    });

    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否成功'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '错误信息'
    });
  }

  execute(context: ExecutionContext): boolean {
    const data = this.getInputValue('data') as string;
    const encoding = this.getInputValue('encoding') as string;
    const urlSafe = this.getInputValue('urlSafe') as boolean;

    if (!data) {
      this.setOutputValue('encoded', '');
      this.setOutputValue('success', false);
      this.setOutputValue('error', '数据不能为空');
      return false;
    }

    try {
      // 获取加密服务
      const cryptoService = context.world?.getService('Cryptography');
      if (!cryptoService) {
        this.setOutputValue('encoded', '');
        this.setOutputValue('success', false);
        this.setOutputValue('error', '加密服务未找到');
        return false;
      }

      // Base64编码
      const encoded = (cryptoService as any).base64Encode(data, { encoding, urlSafe });

      this.setOutputValue('encoded', encoded);
      this.setOutputValue('success', true);
      this.setOutputValue('error', '');
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('Base64编码失败:', errorMessage);
      this.setOutputValue('encoded', '');
      this.setOutputValue('success', false);
      this.setOutputValue('error', errorMessage);
      return false;
    }
  }
}

/**
 * Base64解码节点
 */
export class Base64DecodeNode extends FunctionNode {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'encoded',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: 'Base64编码的数据',
      required: true
    });

    this.addInput({
      name: 'outputEncoding',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '输出编码（utf8, binary）',
      defaultValue: 'utf8'
    });

    this.addInput({
      name: 'urlSafe',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '是否使用URL安全解码',
      defaultValue: false
    });

    // 输出插槽
    this.addOutput({
      name: 'decoded',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '解码后的数据'
    });

    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否成功'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '错误信息'
    });
  }

  execute(context: ExecutionContext): boolean {
    const encoded = this.getInputValue('encoded') as string;
    const outputEncoding = this.getInputValue('outputEncoding') as string;
    const urlSafe = this.getInputValue('urlSafe') as boolean;

    if (!encoded) {
      this.setOutputValue('decoded', '');
      this.setOutputValue('success', false);
      this.setOutputValue('error', '编码数据不能为空');
      return false;
    }

    try {
      // 获取加密服务
      const cryptoService = context.world?.getService('Cryptography');
      if (!cryptoService) {
        this.setOutputValue('decoded', '');
        this.setOutputValue('success', false);
        this.setOutputValue('error', '加密服务未找到');
        return false;
      }

      // Base64解码
      const decoded = (cryptoService as any).base64Decode(encoded, { outputEncoding, urlSafe });

      this.setOutputValue('decoded', decoded);
      this.setOutputValue('success', true);
      this.setOutputValue('error', '');
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('Base64解码失败:', errorMessage);
      this.setOutputValue('decoded', '');
      this.setOutputValue('success', false);
      this.setOutputValue('error', errorMessage);
      return false;
    }
  }
}

/**
 * 注册加密解密节点
 * @param registry 节点注册表
 */
export function registerCryptographyNodes(registry: NodeRegistry): void {
  // 注册MD5哈希节点
  registry.registerNodeType({
    type: 'crypto/md5',
    category: NodeCategory.UTILITY,
    constructor: MD5HashNode,
    label: 'MD5哈希',
    description: '计算数据的MD5哈希值',
    icon: 'lock',
    color: '#722ed1',
    tags: ['crypto', 'hash', 'md5', 'security']
  });

  // 注册SHA256哈希节点
  registry.registerNodeType({
    type: 'crypto/sha256',
    category: NodeCategory.UTILITY,
    constructor: SHA256HashNode,
    label: 'SHA256哈希',
    description: '计算数据的SHA256哈希值',
    icon: 'lock',
    color: '#722ed1',
    tags: ['crypto', 'hash', 'sha256', 'security']
  });

  // 注册AES加密节点
  registry.registerNodeType({
    type: 'crypto/aes-encrypt',
    category: NodeCategory.UTILITY,
    constructor: AESEncryptNode,
    label: 'AES加密',
    description: '使用AES算法加密数据',
    icon: 'safety-certificate',
    color: '#722ed1',
    tags: ['crypto', 'aes', 'encrypt', 'security']
  });

  // 注册AES解密节点
  registry.registerNodeType({
    type: 'crypto/aes-decrypt',
    category: NodeCategory.UTILITY,
    constructor: AESDecryptNode,
    label: 'AES解密',
    description: '使用AES算法解密数据',
    icon: 'unlock',
    color: '#722ed1',
    tags: ['crypto', 'aes', 'decrypt', 'security']
  });

  // 注册Base64编码节点
  registry.registerNodeType({
    type: 'crypto/base64-encode',
    category: NodeCategory.UTILITY,
    constructor: Base64EncodeNode,
    label: 'Base64编码',
    description: '将数据编码为Base64格式',
    icon: 'code',
    color: '#722ed1',
    tags: ['crypto', 'base64', 'encode', 'encoding']
  });

  // 注册Base64解码节点
  registry.registerNodeType({
    type: 'crypto/base64-decode',
    category: NodeCategory.UTILITY,
    constructor: Base64DecodeNode,
    label: 'Base64解码',
    description: '将Base64数据解码为原始格式',
    icon: 'code',
    color: '#722ed1',
    tags: ['crypto', 'base64', 'decode', 'encoding']
  });

  console.log('已注册所有加密解密节点类型');
}
