/**
 * 视觉脚本分布式执行节点
 * 提供分布式计算和远程执行功能
 */
import { AsyncNode } from '../nodes/AsyncNode';
import { FunctionNode } from '../nodes/FunctionNode';
import { NodeCategory, SocketDirection, SocketType } from '../nodes/Node';
import { NodeRegistry } from '../nodes/NodeRegistry';
import { ExecutionContext } from '../execution/ExecutionContext';

/**
 * 远程执行任务接口
 */
export interface RemoteTask {
  id: string;
  script: string;
  inputs: Record<string, any>;
  priority: number;
  timeout: number;
  retryCount: number;
  targetNode?: string;
  metadata?: Record<string, any>;
}

/**
 * 执行结果接口
 */
export interface ExecutionResult {
  taskId: string;
  success: boolean;
  outputs: Record<string, any>;
  executionTime: number;
  nodeId: string;
  error?: string;
  logs?: string[];
}

/**
 * 远程执行节点
 */
export class RemoteExecuteNode extends AsyncNode {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    this.addInput({
      name: 'script',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '要执行的脚本',
      required: true
    });

    this.addInput({
      name: 'inputs',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '脚本输入参数',
      defaultValue: {}
    });

    this.addInput({
      name: 'targetNode',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '目标执行节点ID（可选）'
    });

    this.addInput({
      name: 'priority',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '任务优先级（1-10）',
      defaultValue: 5
    });

    this.addInput({
      name: 'timeout',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '执行超时（毫秒）',
      defaultValue: 30000
    });

    this.addInput({
      name: 'retryCount',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '重试次数',
      defaultValue: 3
    });

    this.addInput({
      name: 'async',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '是否异步执行',
      defaultValue: false
    });

    // 输出插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行失败'
    });

    this.addOutput({
      name: 'onProgress',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行进度更新'
    });

    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '执行结果'
    });

    this.addOutput({
      name: 'outputs',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '脚本输出'
    });

    this.addOutput({
      name: 'taskId',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '任务ID'
    });

    this.addOutput({
      name: 'executionTime',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '执行时间（毫秒）'
    });

    this.addOutput({
      name: 'nodeId',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '执行节点ID'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '错误信息'
    });
  }

  async execute(context: ExecutionContext): Promise<boolean> {
    const script = this.getInputValue('script') as string;
    const inputs = this.getInputValue('inputs') as Record<string, any>;
    const targetNode = this.getInputValue('targetNode') as string;
    const priority = this.getInputValue('priority') as number;
    const timeout = this.getInputValue('timeout') as number;
    const retryCount = this.getInputValue('retryCount') as number;
    const isAsync = this.getInputValue('async') as boolean;

    if (!script) {
      this.setOutputValue('error', '脚本内容不能为空');
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 获取分布式执行服务
      const distributedService = context.world?.getService('DistributedExecution');
      if (!distributedService) {
        this.setOutputValue('error', '分布式执行服务未找到');
        this.triggerFlow('fail');
        return false;
      }

      // 创建远程任务
      const task: RemoteTask = {
        id: this.generateTaskId(),
        script,
        inputs,
        priority,
        timeout,
        retryCount,
        targetNode,
        metadata: {
          nodeId: this.id,
          timestamp: Date.now()
        }
      };

      this.setOutputValue('taskId', task.id);

      if (isAsync) {
        // 异步执行
        (distributedService as any).submitTask(task, {
          onProgress: (progress: any) => {
            this.triggerFlow('onProgress');
          },
          onComplete: (result: ExecutionResult) => {
            this.handleExecutionResult(result);
          }
        });

        // 立即返回任务ID
        this.triggerFlow('success');
        return true;
      } else {
        // 同步执行
        const result = await (distributedService as any).executeTask(task);
        return this.handleExecutionResult(result);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('远程执行失败:', errorMessage);
      this.setOutputValue('error', errorMessage);
      this.triggerFlow('fail');
      return false;
    }
  }

  /**
   * 处理执行结果
   */
  private handleExecutionResult(result: ExecutionResult): boolean {
    this.setOutputValue('result', result);
    this.setOutputValue('outputs', result.outputs);
    this.setOutputValue('executionTime', result.executionTime);
    this.setOutputValue('nodeId', result.nodeId);

    if (result.success) {
      this.triggerFlow('success');
      return true;
    } else {
      this.setOutputValue('error', result.error || '执行失败');
      this.triggerFlow('fail');
      return false;
    }
  }

  /**
   * 生成任务ID
   */
  private generateTaskId(): string {
    return `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

/**
 * 集群状态监控节点
 */
export class ClusterStatusNode extends FunctionNode {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'refresh',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '是否刷新状态',
      defaultValue: true
    });

    this.addInput({
      name: 'includeMetrics',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '是否包含性能指标',
      defaultValue: true
    });

    // 输出插槽
    this.addOutput({
      name: 'clusterInfo',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '集群信息'
    });

    this.addOutput({
      name: 'nodes',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.OUTPUT,
      description: '节点列表'
    });

    this.addOutput({
      name: 'activeNodes',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '活跃节点数'
    });

    this.addOutput({
      name: 'totalTasks',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '总任务数'
    });

    this.addOutput({
      name: 'runningTasks',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '运行中任务数'
    });

    this.addOutput({
      name: 'queuedTasks',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '队列中任务数'
    });

    this.addOutput({
      name: 'avgResponseTime',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '平均响应时间（毫秒）'
    });

    this.addOutput({
      name: 'throughput',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '吞吐量（任务/秒）'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '错误信息'
    });
  }

  execute(context: ExecutionContext): boolean {
    const refresh = this.getInputValue('refresh') as boolean;
    const includeMetrics = this.getInputValue('includeMetrics') as boolean;

    try {
      // 获取分布式执行服务
      const distributedService = context.world?.getService('DistributedExecution');
      if (!distributedService) {
        this.setOutputValue('error', '分布式执行服务未找到');
        return false;
      }

      // 获取集群状态
      const clusterStatus = (distributedService as any).getClusterStatus({ refresh, includeMetrics });

      // 设置输出值
      this.setOutputValue('clusterInfo', clusterStatus);
      this.setOutputValue('nodes', clusterStatus.nodes || []);
      this.setOutputValue('activeNodes', clusterStatus.activeNodes || 0);
      this.setOutputValue('totalTasks', clusterStatus.totalTasks || 0);
      this.setOutputValue('runningTasks', clusterStatus.runningTasks || 0);
      this.setOutputValue('queuedTasks', clusterStatus.queuedTasks || 0);
      this.setOutputValue('avgResponseTime', clusterStatus.avgResponseTime || 0);
      this.setOutputValue('throughput', clusterStatus.throughput || 0);

      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('获取集群状态失败:', errorMessage);
      this.setOutputValue('error', errorMessage);
      return false;
    }
  }
}

/**
 * 负载均衡节点
 */
export class LoadBalancerNode extends AsyncNode {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    this.addInput({
      name: 'tasks',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.INPUT,
      description: '任务列表',
      required: true
    });

    this.addInput({
      name: 'strategy',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '负载均衡策略（round_robin, least_connections, weighted, cpu_based）',
      defaultValue: 'round_robin'
    });

    this.addInput({
      name: 'maxConcurrency',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '最大并发数',
      defaultValue: 10
    });

    this.addInput({
      name: 'timeout',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '任务超时（毫秒）',
      defaultValue: 60000
    });

    this.addInput({
      name: 'retryPolicy',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '重试策略',
      defaultValue: { maxRetries: 3, backoffMs: 1000 }
    });

    // 输出插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '分发成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '分发失败'
    });

    this.addOutput({
      name: 'onProgress',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行进度'
    });

    this.addOutput({
      name: 'onComplete',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '全部完成'
    });

    this.addOutput({
      name: 'results',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.OUTPUT,
      description: '执行结果列表'
    });

    this.addOutput({
      name: 'completedTasks',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '已完成任务数'
    });

    this.addOutput({
      name: 'failedTasks',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '失败任务数'
    });

    this.addOutput({
      name: 'totalExecutionTime',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '总执行时间（毫秒）'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '错误信息'
    });
  }

  async execute(context: ExecutionContext): Promise<boolean> {
    const tasks = this.getInputValue('tasks') as any[];
    const strategy = this.getInputValue('strategy') as string;
    const maxConcurrency = this.getInputValue('maxConcurrency') as number;
    const timeout = this.getInputValue('timeout') as number;
    const retryPolicy = this.getInputValue('retryPolicy') as any;

    if (!tasks || tasks.length === 0) {
      this.setOutputValue('error', '任务列表不能为空');
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 获取分布式执行服务
      const distributedService = context.world?.getService('DistributedExecution');
      if (!distributedService) {
        this.setOutputValue('error', '分布式执行服务未找到');
        this.triggerFlow('fail');
        return false;
      }

      const startTime = Date.now();
      let completedTasks = 0;
      let failedTasks = 0;
      const results: any[] = [];

      // 执行负载均衡分发
      const balancer = await (distributedService as any).createLoadBalancer({
        strategy,
        maxConcurrency,
        timeout,
        retryPolicy,
        onProgress: (progress: any) => {
          completedTasks = progress.completed;
          failedTasks = progress.failed;
          this.setOutputValue('completedTasks', completedTasks);
          this.setOutputValue('failedTasks', failedTasks);
          this.triggerFlow('onProgress');
        }
      });

      const allResults = await balancer.distributeTasks(tasks);

      // 统计结果
      for (const result of allResults) {
        results.push(result);
        if (result.success) {
          completedTasks++;
        } else {
          failedTasks++;
        }
      }

      const totalExecutionTime = Date.now() - startTime;

      // 设置输出值
      this.setOutputValue('results', results);
      this.setOutputValue('completedTasks', completedTasks);
      this.setOutputValue('failedTasks', failedTasks);
      this.setOutputValue('totalExecutionTime', totalExecutionTime);

      // 触发完成事件
      this.triggerFlow('onComplete');

      if (failedTasks === 0) {
        this.triggerFlow('success');
      } else {
        this.setOutputValue('error', `${failedTasks} 个任务执行失败`);
        this.triggerFlow('fail');
      }

      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('负载均衡执行失败:', errorMessage);
      this.setOutputValue('error', errorMessage);
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 任务调度节点
 */
export class TaskSchedulerNode extends AsyncNode {
  private scheduler: any = null;

  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'start',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '启动调度器'
    });

    this.addInput({
      name: 'stop',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '停止调度器'
    });

    this.addInput({
      name: 'addTask',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '添加任务'
    });

    this.addInput({
      name: 'task',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '要添加的任务'
    });

    this.addInput({
      name: 'schedule',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '调度表达式（cron格式）'
    });

    this.addInput({
      name: 'maxConcurrentTasks',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '最大并发任务数',
      defaultValue: 5
    });

    this.addInput({
      name: 'queueSize',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '队列大小',
      defaultValue: 100
    });

    // 输出插槽
    this.addOutput({
      name: 'onStart',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '调度器启动'
    });

    this.addOutput({
      name: 'onStop',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '调度器停止'
    });

    this.addOutput({
      name: 'onTaskExecute',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '任务执行'
    });

    this.addOutput({
      name: 'onTaskComplete',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '任务完成'
    });

    this.addOutput({
      name: 'scheduler',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '调度器对象'
    });

    this.addOutput({
      name: 'isRunning',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否运行中'
    });

    this.addOutput({
      name: 'queuedTasks',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '队列中任务数'
    });

    this.addOutput({
      name: 'runningTasks',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '运行中任务数'
    });

    this.addOutput({
      name: 'executedTask',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '当前执行的任务'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '错误信息'
    });
  }

  async execute(context: ExecutionContext): Promise<boolean> {
    const inputSocket = this.getTriggeredInputSocket();

    try {
      // 获取分布式执行服务
      const distributedService = context.world?.getService('DistributedExecution');
      if (!distributedService) {
        this.setOutputValue('error', '分布式执行服务未找到');
        return false;
      }

      if (inputSocket?.name === 'start') {
        return this.startScheduler(distributedService);
      } else if (inputSocket?.name === 'stop') {
        return this.stopScheduler();
      } else if (inputSocket?.name === 'addTask') {
        return this.addTask();
      }

      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('任务调度操作失败:', errorMessage);
      this.setOutputValue('error', errorMessage);
      return false;
    }
  }

  /**
   * 启动调度器
   */
  private async startScheduler(distributedService: any): Promise<boolean> {
    const maxConcurrentTasks = this.getInputValue('maxConcurrentTasks') as number;
    const queueSize = this.getInputValue('queueSize') as number;

    this.scheduler = await distributedService.createScheduler({
      maxConcurrentTasks,
      queueSize,
      onTaskExecute: (task: any) => {
        this.setOutputValue('executedTask', task);
        this.updateTaskCounts();
        this.triggerFlow('onTaskExecute');
      },
      onTaskComplete: (task: any, result: any) => {
        this.setOutputValue('executedTask', task);
        this.updateTaskCounts();
        this.triggerFlow('onTaskComplete');
      }
    });

    this.setOutputValue('scheduler', this.scheduler);
    this.setOutputValue('isRunning', true);
    this.updateTaskCounts();
    this.triggerFlow('onStart');

    return true;
  }

  /**
   * 停止调度器
   */
  private async stopScheduler(): Promise<boolean> {
    if (this.scheduler) {
      await this.scheduler.stop();
      this.scheduler = null;
      this.setOutputValue('isRunning', false);
      this.setOutputValue('queuedTasks', 0);
      this.setOutputValue('runningTasks', 0);
      this.triggerFlow('onStop');
    }
    return true;
  }

  /**
   * 添加任务
   */
  private addTask(): boolean {
    const task = this.getInputValue('task');
    const schedule = this.getInputValue('schedule') as string;

    if (!this.scheduler) {
      this.setOutputValue('error', '调度器未启动');
      return false;
    }

    if (!task) {
      this.setOutputValue('error', '任务不能为空');
      return false;
    }

    this.scheduler.addTask(task, schedule);
    this.updateTaskCounts();
    return true;
  }

  /**
   * 更新任务计数
   */
  private updateTaskCounts(): void {
    if (this.scheduler) {
      const status = this.scheduler.getStatus();
      this.setOutputValue('queuedTasks', status.queuedTasks || 0);
      this.setOutputValue('runningTasks', status.runningTasks || 0);
    }
  }

  /**
   * 节点销毁时清理调度器
   */
  public destroy(): void {
    this.stopScheduler();
    super.destroy();
  }
}

/**
 * 注册分布式执行节点
 * @param registry 节点注册表
 */
export function registerDistributedExecutionNodes(registry: NodeRegistry): void {
  // 注册远程执行节点
  registry.registerNodeType({
    type: 'distributed/remote-execute',
    category: NodeCategory.NETWORK,
    constructor: RemoteExecuteNode,
    label: '远程执行',
    description: '在远程节点上执行脚本任务',
    icon: 'cloud',
    color: '#1890ff',
    tags: ['distributed', 'remote', 'execute', 'cluster']
  });

  // 注册集群状态监控节点
  registry.registerNodeType({
    type: 'distributed/cluster-status',
    category: NodeCategory.NETWORK,
    constructor: ClusterStatusNode,
    label: '集群状态',
    description: '获取分布式集群的状态信息',
    icon: 'cluster',
    color: '#1890ff',
    tags: ['distributed', 'cluster', 'status', 'monitoring']
  });

  // 注册负载均衡节点
  registry.registerNodeType({
    type: 'distributed/load-balancer',
    category: NodeCategory.NETWORK,
    constructor: LoadBalancerNode,
    label: '负载均衡',
    description: '将任务分发到多个执行节点',
    icon: 'deployment-unit',
    color: '#1890ff',
    tags: ['distributed', 'load-balancer', 'scaling', 'performance']
  });

  // 注册任务调度节点
  registry.registerNodeType({
    type: 'distributed/task-scheduler',
    category: NodeCategory.NETWORK,
    constructor: TaskSchedulerNode,
    label: '任务调度',
    description: '管理和调度分布式任务执行',
    icon: 'schedule',
    color: '#1890ff',
    tags: ['distributed', 'scheduler', 'cron', 'automation']
  });

  console.log('已注册所有分布式执行节点类型');
}
