/**
 * 视觉脚本性能分析节点
 * 提供代码性能分析和优化建议功能
 */
import { AsyncNode } from '../nodes/AsyncNode';
import { FunctionNode } from '../nodes/FunctionNode';
import { NodeCategory, SocketDirection, SocketType } from '../nodes/Node';
import { NodeRegistry } from '../nodes/NodeRegistry';
import { ExecutionContext } from '../execution/ExecutionContext';

/**
 * 性能分析结果接口
 */
export interface PerformanceProfile {
  id: string;
  target: string;
  duration: number;
  samples: number;
  functions: FunctionProfile[];
  hotspots: Hotspot[];
  memoryUsage: MemoryProfile;
  cpuUsage: CpuProfile;
  recommendations: string[];
}

/**
 * 函数性能分析接口
 */
export interface FunctionProfile {
  name: string;
  file: string;
  line: number;
  selfTime: number;
  totalTime: number;
  callCount: number;
  avgTime: number;
  percentage: number;
}

/**
 * 性能热点接口
 */
export interface Hotspot {
  location: string;
  type: 'cpu' | 'memory' | 'io';
  severity: 'low' | 'medium' | 'high' | 'critical';
  impact: number;
  description: string;
  suggestion: string;
}

/**
 * 内存分析接口
 */
export interface MemoryProfile {
  heapUsed: number;
  heapTotal: number;
  external: number;
  arrayBuffers: number;
  peakUsage: number;
  allocations: number;
  deallocations: number;
  leaks: MemoryLeak[];
}

/**
 * 内存泄漏接口
 */
export interface MemoryLeak {
  type: string;
  size: number;
  location: string;
  retainedSize: number;
  description: string;
}

/**
 * CPU分析接口
 */
export interface CpuProfile {
  totalTime: number;
  idleTime: number;
  userTime: number;
  systemTime: number;
  utilization: number;
  threads: ThreadProfile[];
}

/**
 * 线程分析接口
 */
export interface ThreadProfile {
  id: string;
  name: string;
  cpuTime: number;
  state: string;
  stackTrace: string[];
}

/**
 * 性能分析器节点
 */
export class PerformanceProfilerNode extends AsyncNode {
  private profiler: any = null;

  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'start',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '开始性能分析'
    });

    this.addInput({
      name: 'stop',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '停止性能分析'
    });

    this.addInput({
      name: 'target',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '分析目标',
      required: true
    });

    this.addInput({
      name: 'profileType',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '分析类型（cpu, memory, heap, sampling）',
      defaultValue: 'cpu'
    });

    this.addInput({
      name: 'duration',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '分析持续时间（毫秒）',
      defaultValue: 10000
    });

    this.addInput({
      name: 'sampleInterval',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '采样间隔（毫秒）',
      defaultValue: 100
    });

    this.addInput({
      name: 'includeNative',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '是否包含原生代码',
      defaultValue: false
    });

    this.addInput({
      name: 'trackAllocations',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '是否跟踪内存分配',
      defaultValue: true
    });

    // 输出插槽
    this.addOutput({
      name: 'onStart',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '分析已开始'
    });

    this.addOutput({
      name: 'onStop',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '分析已停止'
    });

    this.addOutput({
      name: 'onComplete',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '分析已完成'
    });

    this.addOutput({
      name: 'profile',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '性能分析结果'
    });

    this.addOutput({
      name: 'functions',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.OUTPUT,
      description: '函数性能数据'
    });

    this.addOutput({
      name: 'hotspots',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.OUTPUT,
      description: '性能热点'
    });

    this.addOutput({
      name: 'memoryProfile',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '内存分析结果'
    });

    this.addOutput({
      name: 'cpuProfile',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: 'CPU分析结果'
    });

    this.addOutput({
      name: 'recommendations',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.OUTPUT,
      description: '优化建议'
    });

    this.addOutput({
      name: 'isProfilering',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否正在分析'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '错误信息'
    });
  }

  async execute(context: ExecutionContext): Promise<boolean> {
    const inputSocket = this.getTriggeredInputSocket();

    try {
      // 获取性能分析服务
      const performanceService = context.world?.getService('PerformanceAnalysis');
      if (!performanceService) {
        this.setOutputValue('error', '性能分析服务未找到');
        return false;
      }

      if (inputSocket?.name === 'start') {
        return this.startProfiling(performanceService);
      } else if (inputSocket?.name === 'stop') {
        return this.stopProfiling(performanceService);
      }

      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('性能分析操作失败:', errorMessage);
      this.setOutputValue('error', errorMessage);
      return false;
    }
  }

  /**
   * 开始性能分析
   */
  private async startProfiling(performanceService: any): Promise<boolean> {
    const target = this.getInputValue('target') as string;
    const profileType = this.getInputValue('profileType') as string;
    const duration = this.getInputValue('duration') as number;
    const sampleInterval = this.getInputValue('sampleInterval') as number;
    const includeNative = this.getInputValue('includeNative') as boolean;
    const trackAllocations = this.getInputValue('trackAllocations') as boolean;

    if (!target) {
      this.setOutputValue('error', '分析目标不能为空');
      return false;
    }

    this.profiler = await performanceService.createProfiler({
      target,
      profileType,
      duration,
      sampleInterval,
      includeNative,
      trackAllocations,
      onComplete: (profile: PerformanceProfile) => {
        this.handleProfileComplete(profile);
      }
    });

    await this.profiler.start();

    this.setOutputValue('isProfilering', true);
    this.triggerFlow('onStart');

    return true;
  }

  /**
   * 停止性能分析
   */
  private async stopProfiling(performanceService: any): Promise<boolean> {
    if (this.profiler) {
      const profile = await this.profiler.stop();
      this.handleProfileComplete(profile);
      this.profiler = null;
    }

    this.setOutputValue('isProfilering', false);
    this.triggerFlow('onStop');

    return true;
  }

  /**
   * 处理分析完成
   */
  private handleProfileComplete(profile: PerformanceProfile): void {
    // 设置输出值
    this.setOutputValue('profile', profile);
    this.setOutputValue('functions', profile.functions);
    this.setOutputValue('hotspots', profile.hotspots);
    this.setOutputValue('memoryProfile', profile.memoryUsage);
    this.setOutputValue('cpuProfile', profile.cpuUsage);
    this.setOutputValue('recommendations', profile.recommendations);
    this.setOutputValue('isProfilering', false);

    // 触发完成事件
    this.triggerFlow('onComplete');
  }

  /**
   * 节点销毁时清理分析器
   */
  public destroy(): void {
    if (this.profiler) {
      this.profiler.stop();
      this.profiler = null;
    }
    super.destroy();
  }
}

/**
 * 内存分析节点
 */
export class MemoryAnalysisNode extends AsyncNode {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    this.addInput({
      name: 'target',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '分析目标',
      required: true
    });

    this.addInput({
      name: 'analysisType',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '分析类型（heap, leaks, allocations, gc）',
      defaultValue: 'heap'
    });

    this.addInput({
      name: 'includeDetails',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '是否包含详细信息',
      defaultValue: true
    });

    this.addInput({
      name: 'threshold',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '内存阈值（MB）',
      defaultValue: 10
    });

    // 输出插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '分析成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '分析失败'
    });

    this.addOutput({
      name: 'memoryProfile',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '内存分析结果'
    });

    this.addOutput({
      name: 'memoryLeaks',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.OUTPUT,
      description: '内存泄漏列表'
    });

    this.addOutput({
      name: 'heapSnapshot',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '堆快照'
    });

    this.addOutput({
      name: 'gcStats',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '垃圾回收统计'
    });

    this.addOutput({
      name: 'recommendations',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.OUTPUT,
      description: '优化建议'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '错误信息'
    });
  }

  async execute(context: ExecutionContext): Promise<boolean> {
    const target = this.getInputValue('target') as string;
    const analysisType = this.getInputValue('analysisType') as string;
    const includeDetails = this.getInputValue('includeDetails') as boolean;
    const threshold = this.getInputValue('threshold') as number;

    if (!target) {
      this.setOutputValue('error', '分析目标不能为空');
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 获取性能分析服务
      const performanceService = context.world?.getService('PerformanceAnalysis');
      if (!performanceService) {
        this.setOutputValue('error', '性能分析服务未找到');
        this.triggerFlow('fail');
        return false;
      }

      // 执行内存分析
      const result = await (performanceService as any).analyzeMemory({
        target,
        analysisType,
        includeDetails,
        threshold
      });

      // 设置输出值
      this.setOutputValue('memoryProfile', result.profile);
      this.setOutputValue('memoryLeaks', result.leaks || []);
      this.setOutputValue('heapSnapshot', result.heapSnapshot);
      this.setOutputValue('gcStats', result.gcStats);
      this.setOutputValue('recommendations', result.recommendations || []);

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('内存分析失败:', errorMessage);
      this.setOutputValue('error', errorMessage);
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * CPU分析节点
 */
export class CpuAnalysisNode extends AsyncNode {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    this.addInput({
      name: 'target',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '分析目标',
      required: true
    });

    this.addInput({
      name: 'duration',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '分析持续时间（毫秒）',
      defaultValue: 5000
    });

    this.addInput({
      name: 'sampleRate',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '采样频率（Hz）',
      defaultValue: 1000
    });

    this.addInput({
      name: 'includeThreads',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '是否包含线程分析',
      defaultValue: true
    });

    this.addInput({
      name: 'trackCallStack',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '是否跟踪调用栈',
      defaultValue: true
    });

    // 输出插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '分析成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '分析失败'
    });

    this.addOutput({
      name: 'cpuProfile',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: 'CPU分析结果'
    });

    this.addOutput({
      name: 'functions',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.OUTPUT,
      description: '函数性能数据'
    });

    this.addOutput({
      name: 'threads',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.OUTPUT,
      description: '线程分析数据'
    });

    this.addOutput({
      name: 'callGraph',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '调用图'
    });

    this.addOutput({
      name: 'bottlenecks',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.OUTPUT,
      description: '性能瓶颈'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '错误信息'
    });
  }

  async execute(context: ExecutionContext): Promise<boolean> {
    const target = this.getInputValue('target') as string;
    const duration = this.getInputValue('duration') as number;
    const sampleRate = this.getInputValue('sampleRate') as number;
    const includeThreads = this.getInputValue('includeThreads') as boolean;
    const trackCallStack = this.getInputValue('trackCallStack') as boolean;

    if (!target) {
      this.setOutputValue('error', '分析目标不能为空');
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 获取性能分析服务
      const performanceService = context.world?.getService('PerformanceAnalysis');
      if (!performanceService) {
        this.setOutputValue('error', '性能分析服务未找到');
        this.triggerFlow('fail');
        return false;
      }

      // 执行CPU分析
      const result = await (performanceService as any).analyzeCpu({
        target,
        duration,
        sampleRate,
        includeThreads,
        trackCallStack
      });

      // 设置输出值
      this.setOutputValue('cpuProfile', result.profile);
      this.setOutputValue('functions', result.functions || []);
      this.setOutputValue('threads', result.threads || []);
      this.setOutputValue('callGraph', result.callGraph);
      this.setOutputValue('bottlenecks', result.bottlenecks || []);

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('CPU分析失败:', errorMessage);
      this.setOutputValue('error', errorMessage);
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 注册性能分析节点
 * @param registry 节点注册表
 */
export function registerPerformanceAnalysisNodes(registry: NodeRegistry): void {
  // 注册性能分析器节点
  registry.registerNodeType({
    type: 'performance/profiler',
    category: NodeCategory.UTILITY,
    constructor: PerformanceProfilerNode,
    label: '性能分析器',
    description: '全面的性能分析和优化建议',
    icon: 'dashboard',
    color: '#fa541c',
    tags: ['performance', 'profiler', 'analysis', 'optimization']
  });

  // 注册内存分析节点
  registry.registerNodeType({
    type: 'performance/memory-analysis',
    category: NodeCategory.UTILITY,
    constructor: MemoryAnalysisNode,
    label: '内存分析',
    description: '内存使用分析和泄漏检测',
    icon: 'database',
    color: '#fa541c',
    tags: ['performance', 'memory', 'heap', 'leaks']
  });

  // 注册CPU分析节点
  registry.registerNodeType({
    type: 'performance/cpu-analysis',
    category: NodeCategory.UTILITY,
    constructor: CpuAnalysisNode,
    label: 'CPU分析',
    description: 'CPU使用分析和瓶颈识别',
    icon: 'thunderbolt',
    color: '#fa541c',
    tags: ['performance', 'cpu', 'bottleneck', 'optimization']
  });

  console.log('已注册所有性能分析节点类型');
}
