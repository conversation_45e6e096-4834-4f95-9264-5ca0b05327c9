/**
 * 视觉脚本性能监控节点
 * 提供系统性能监控和分析功能
 */
import { FunctionNode } from '../nodes/FunctionNode';
import { AsyncNode } from '../nodes/AsyncNode';
import { NodeCategory, SocketDirection, SocketType } from '../nodes/Node';
import { NodeRegistry } from '../nodes/NodeRegistry';
import { ExecutionContext } from '../execution/ExecutionContext';

/**
 * 性能指标接口
 */
export interface PerformanceMetrics {
  timestamp: number;
  cpu: {
    usage: number;
    cores: number;
    loadAverage: number[];
  };
  memory: {
    used: number;
    total: number;
    free: number;
    usage: number;
  };
  network: {
    bytesIn: number;
    bytesOut: number;
    packetsIn: number;
    packetsOut: number;
  };
  disk: {
    used: number;
    total: number;
    free: number;
    usage: number;
    readOps: number;
    writeOps: number;
  };
  processes: {
    total: number;
    running: number;
    sleeping: number;
  };
}

/**
 * 系统性能监控节点
 */
export class SystemPerformanceNode extends FunctionNode {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'includeProcesses',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '是否包含进程信息',
      defaultValue: true
    });

    this.addInput({
      name: 'includeNetwork',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '是否包含网络信息',
      defaultValue: true
    });

    this.addInput({
      name: 'includeDisk',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '是否包含磁盘信息',
      defaultValue: true
    });

    // 输出插槽
    this.addOutput({
      name: 'metrics',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '性能指标'
    });

    this.addOutput({
      name: 'cpuUsage',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: 'CPU使用率（%）'
    });

    this.addOutput({
      name: 'memoryUsage',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '内存使用率（%）'
    });

    this.addOutput({
      name: 'diskUsage',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '磁盘使用率（%）'
    });

    this.addOutput({
      name: 'loadAverage',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.OUTPUT,
      description: '系统负载平均值'
    });

    this.addOutput({
      name: 'networkTraffic',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '网络流量信息'
    });

    this.addOutput({
      name: 'processCount',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '进程总数'
    });

    this.addOutput({
      name: 'timestamp',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '采集时间戳'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '错误信息'
    });
  }

  execute(context: ExecutionContext): boolean {
    const includeProcesses = this.getInputValue('includeProcesses') as boolean;
    const includeNetwork = this.getInputValue('includeNetwork') as boolean;
    const includeDisk = this.getInputValue('includeDisk') as boolean;

    try {
      // 获取性能监控服务
      const performanceService = context.world?.getService('PerformanceMonitoring');
      if (!performanceService) {
        this.setOutputValue('error', '性能监控服务未找到');
        return false;
      }

      // 获取系统性能指标
      const metrics = (performanceService as any).getSystemMetrics({
        includeProcesses,
        includeNetwork,
        includeDisk
      });

      // 设置输出值
      this.setOutputValue('metrics', metrics);
      this.setOutputValue('cpuUsage', metrics.cpu.usage);
      this.setOutputValue('memoryUsage', metrics.memory.usage);
      this.setOutputValue('diskUsage', metrics.disk.usage);
      this.setOutputValue('loadAverage', metrics.cpu.loadAverage);
      this.setOutputValue('networkTraffic', metrics.network);
      this.setOutputValue('processCount', metrics.processes.total);
      this.setOutputValue('timestamp', metrics.timestamp);

      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('获取系统性能指标失败:', errorMessage);
      this.setOutputValue('error', errorMessage);
      return false;
    }
  }
}

/**
 * 应用性能监控节点
 */
export class ApplicationPerformanceNode extends AsyncNode {
  private monitor: any = null;

  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'start',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '开始监控'
    });

    this.addInput({
      name: 'stop',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '停止监控'
    });

    this.addInput({
      name: 'applicationId',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '应用程序ID',
      required: true
    });

    this.addInput({
      name: 'interval',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '监控间隔（毫秒）',
      defaultValue: 5000
    });

    this.addInput({
      name: 'metrics',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.INPUT,
      description: '要监控的指标列表',
      defaultValue: ['cpu', 'memory', 'response_time', 'throughput']
    });

    this.addInput({
      name: 'alertThresholds',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '告警阈值',
      defaultValue: { cpu: 80, memory: 85, response_time: 1000 }
    });

    // 输出插槽
    this.addOutput({
      name: 'onStart',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '监控开始'
    });

    this.addOutput({
      name: 'onStop',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '监控停止'
    });

    this.addOutput({
      name: 'onMetrics',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '指标更新'
    });

    this.addOutput({
      name: 'onAlert',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '告警触发'
    });

    this.addOutput({
      name: 'currentMetrics',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '当前指标'
    });

    this.addOutput({
      name: 'isMonitoring',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否监控中'
    });

    this.addOutput({
      name: 'alertInfo',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '告警信息'
    });

    this.addOutput({
      name: 'monitoringDuration',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '监控持续时间（毫秒）'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '错误信息'
    });
  }

  async execute(context: ExecutionContext): Promise<boolean> {
    const inputSocket = this.getTriggeredInputSocket();

    try {
      // 获取性能监控服务
      const performanceService = context.world?.getService('PerformanceMonitoring');
      if (!performanceService) {
        this.setOutputValue('error', '性能监控服务未找到');
        return false;
      }

      if (inputSocket?.name === 'start') {
        return this.startMonitoring(performanceService);
      } else if (inputSocket?.name === 'stop') {
        return this.stopMonitoring();
      }

      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('应用性能监控操作失败:', errorMessage);
      this.setOutputValue('error', errorMessage);
      return false;
    }
  }

  /**
   * 开始监控
   */
  private async startMonitoring(performanceService: any): Promise<boolean> {
    const applicationId = this.getInputValue('applicationId') as string;
    const interval = this.getInputValue('interval') as number;
    const metrics = this.getInputValue('metrics') as string[];
    const alertThresholds = this.getInputValue('alertThresholds') as any;

    if (!applicationId) {
      this.setOutputValue('error', '应用程序ID不能为空');
      return false;
    }

    const startTime = Date.now();

    this.monitor = await performanceService.createApplicationMonitor({
      applicationId,
      interval,
      metrics,
      alertThresholds,
      onMetrics: (metricsData: any) => {
        this.setOutputValue('currentMetrics', metricsData);
        this.setOutputValue('monitoringDuration', Date.now() - startTime);
        this.triggerFlow('onMetrics');
      },
      onAlert: (alertData: any) => {
        this.setOutputValue('alertInfo', alertData);
        this.triggerFlow('onAlert');
      }
    });

    this.setOutputValue('isMonitoring', true);
    this.setOutputValue('monitoringDuration', 0);
    this.triggerFlow('onStart');

    return true;
  }

  /**
   * 停止监控
   */
  private async stopMonitoring(): Promise<boolean> {
    if (this.monitor) {
      await this.monitor.stop();
      this.monitor = null;
      this.setOutputValue('isMonitoring', false);
      this.triggerFlow('onStop');
    }
    return true;
  }

  /**
   * 节点销毁时清理监控器
   */
  public destroy(): void {
    this.stopMonitoring();
    super.destroy();
  }
}

/**
 * 性能分析节点
 */
export class PerformanceAnalysisNode extends AsyncNode {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    this.addInput({
      name: 'metricsData',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.INPUT,
      description: '性能指标数据',
      required: true
    });

    this.addInput({
      name: 'timeRange',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '时间范围 {start, end}',
      required: true
    });

    this.addInput({
      name: 'analysisType',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '分析类型（trend, anomaly, correlation, forecast）',
      defaultValue: 'trend'
    });

    this.addInput({
      name: 'aggregation',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '聚合方式（avg, max, min, sum, p95, p99）',
      defaultValue: 'avg'
    });

    this.addInput({
      name: 'groupBy',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '分组字段',
      defaultValue: 'timestamp'
    });

    // 输出插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '分析成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '分析失败'
    });

    this.addOutput({
      name: 'analysisResult',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '分析结果'
    });

    this.addOutput({
      name: 'trends',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.OUTPUT,
      description: '趋势数据'
    });

    this.addOutput({
      name: 'anomalies',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.OUTPUT,
      description: '异常点'
    });

    this.addOutput({
      name: 'correlations',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '相关性分析'
    });

    this.addOutput({
      name: 'forecast',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.OUTPUT,
      description: '预测数据'
    });

    this.addOutput({
      name: 'statistics',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '统计信息'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '错误信息'
    });
  }

  async execute(context: ExecutionContext): Promise<boolean> {
    const metricsData = this.getInputValue('metricsData') as any[];
    const timeRange = this.getInputValue('timeRange') as any;
    const analysisType = this.getInputValue('analysisType') as string;
    const aggregation = this.getInputValue('aggregation') as string;
    const groupBy = this.getInputValue('groupBy') as string;

    if (!metricsData || metricsData.length === 0) {
      this.setOutputValue('error', '性能指标数据不能为空');
      this.triggerFlow('fail');
      return false;
    }

    if (!timeRange || !timeRange.start || !timeRange.end) {
      this.setOutputValue('error', '时间范围不能为空');
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 获取性能监控服务
      const performanceService = context.world?.getService('PerformanceMonitoring');
      if (!performanceService) {
        this.setOutputValue('error', '性能监控服务未找到');
        this.triggerFlow('fail');
        return false;
      }

      // 执行性能分析
      const analysisResult = await (performanceService as any).analyzePerformance({
        data: metricsData,
        timeRange,
        analysisType,
        aggregation,
        groupBy
      });

      // 设置输出值
      this.setOutputValue('analysisResult', analysisResult);
      this.setOutputValue('trends', analysisResult.trends || []);
      this.setOutputValue('anomalies', analysisResult.anomalies || []);
      this.setOutputValue('correlations', analysisResult.correlations || {});
      this.setOutputValue('forecast', analysisResult.forecast || []);
      this.setOutputValue('statistics', analysisResult.statistics || {});

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('性能分析失败:', errorMessage);
      this.setOutputValue('error', errorMessage);
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 日志监控节点
 */
export class LogMonitoringNode extends AsyncNode {
  private logMonitor: any = null;

  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'start',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '开始监控'
    });

    this.addInput({
      name: 'stop',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '停止监控'
    });

    this.addInput({
      name: 'logSources',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.INPUT,
      description: '日志源列表',
      required: true
    });

    this.addInput({
      name: 'filters',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '日志过滤器',
      defaultValue: {}
    });

    this.addInput({
      name: 'alertRules',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.INPUT,
      description: '告警规则',
      defaultValue: []
    });

    this.addInput({
      name: 'bufferSize',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '缓冲区大小',
      defaultValue: 1000
    });

    this.addInput({
      name: 'batchSize',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '批处理大小',
      defaultValue: 100
    });

    // 输出插槽
    this.addOutput({
      name: 'onStart',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '监控开始'
    });

    this.addOutput({
      name: 'onStop',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '监控停止'
    });

    this.addOutput({
      name: 'onLogEntry',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '新日志条目'
    });

    this.addOutput({
      name: 'onAlert',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '日志告警'
    });

    this.addOutput({
      name: 'onBatch',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '批量日志'
    });

    this.addOutput({
      name: 'logEntry',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '当前日志条目'
    });

    this.addOutput({
      name: 'logBatch',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.OUTPUT,
      description: '日志批次'
    });

    this.addOutput({
      name: 'alertData',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '告警数据'
    });

    this.addOutput({
      name: 'isMonitoring',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否监控中'
    });

    this.addOutput({
      name: 'processedLogs',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '已处理日志数'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '错误信息'
    });
  }

  async execute(context: ExecutionContext): Promise<boolean> {
    const inputSocket = this.getTriggeredInputSocket();

    try {
      // 获取性能监控服务
      const performanceService = context.world?.getService('PerformanceMonitoring');
      if (!performanceService) {
        this.setOutputValue('error', '性能监控服务未找到');
        return false;
      }

      if (inputSocket?.name === 'start') {
        return this.startLogMonitoring(performanceService);
      } else if (inputSocket?.name === 'stop') {
        return this.stopLogMonitoring();
      }

      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('日志监控操作失败:', errorMessage);
      this.setOutputValue('error', errorMessage);
      return false;
    }
  }

  /**
   * 开始日志监控
   */
  private async startLogMonitoring(performanceService: any): Promise<boolean> {
    const logSources = this.getInputValue('logSources') as string[];
    const filters = this.getInputValue('filters') as any;
    const alertRules = this.getInputValue('alertRules') as any[];
    const bufferSize = this.getInputValue('bufferSize') as number;
    const batchSize = this.getInputValue('batchSize') as number;

    if (!logSources || logSources.length === 0) {
      this.setOutputValue('error', '日志源不能为空');
      return false;
    }

    let processedCount = 0;

    this.logMonitor = await performanceService.createLogMonitor({
      sources: logSources,
      filters,
      alertRules,
      bufferSize,
      batchSize,
      onLogEntry: (logEntry: any) => {
        processedCount++;
        this.setOutputValue('logEntry', logEntry);
        this.setOutputValue('processedLogs', processedCount);
        this.triggerFlow('onLogEntry');
      },
      onBatch: (logBatch: any[]) => {
        this.setOutputValue('logBatch', logBatch);
        this.triggerFlow('onBatch');
      },
      onAlert: (alertData: any) => {
        this.setOutputValue('alertData', alertData);
        this.triggerFlow('onAlert');
      }
    });

    this.setOutputValue('isMonitoring', true);
    this.setOutputValue('processedLogs', 0);
    this.triggerFlow('onStart');

    return true;
  }

  /**
   * 停止日志监控
   */
  private async stopLogMonitoring(): Promise<boolean> {
    if (this.logMonitor) {
      await this.logMonitor.stop();
      this.logMonitor = null;
      this.setOutputValue('isMonitoring', false);
      this.triggerFlow('onStop');
    }
    return true;
  }

  /**
   * 节点销毁时清理监控器
   */
  public destroy(): void {
    this.stopLogMonitoring();
    super.destroy();
  }
}

/**
 * 注册性能监控节点
 * @param registry 节点注册表
 */
export function registerPerformanceMonitoringNodes(registry: NodeRegistry): void {
  // 注册系统性能监控节点
  registry.registerNodeType({
    type: 'performance/system-metrics',
    category: NodeCategory.UTILITY,
    constructor: SystemPerformanceNode,
    label: '系统性能监控',
    description: '获取系统性能指标（CPU、内存、磁盘、网络）',
    icon: 'dashboard',
    color: '#fa8c16',
    tags: ['performance', 'system', 'metrics', 'monitoring']
  });

  // 注册应用性能监控节点
  registry.registerNodeType({
    type: 'performance/application-monitor',
    category: NodeCategory.UTILITY,
    constructor: ApplicationPerformanceNode,
    label: '应用性能监控',
    description: '监控应用程序的性能指标和告警',
    icon: 'monitor',
    color: '#fa8c16',
    tags: ['performance', 'application', 'monitoring', 'alerts']
  });

  // 注册性能分析节点
  registry.registerNodeType({
    type: 'performance/analysis',
    category: NodeCategory.UTILITY,
    constructor: PerformanceAnalysisNode,
    label: '性能分析',
    description: '分析性能数据，识别趋势和异常',
    icon: 'line-chart',
    color: '#fa8c16',
    tags: ['performance', 'analysis', 'trends', 'anomaly']
  });

  // 注册日志监控节点
  registry.registerNodeType({
    type: 'performance/log-monitoring',
    category: NodeCategory.UTILITY,
    constructor: LogMonitoringNode,
    label: '日志监控',
    description: '监控日志文件，提供实时日志分析和告警',
    icon: 'file-text',
    color: '#fa8c16',
    tags: ['performance', 'logs', 'monitoring', 'alerts']
  });

  console.log('已注册所有性能监控节点类型');
}
