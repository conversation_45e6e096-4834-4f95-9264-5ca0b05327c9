/**
 * 视觉脚本高级调试节点
 * 提供专业级调试和性能分析功能
 */
import { AsyncNode } from '../nodes/AsyncNode';
import { FunctionNode } from '../nodes/FunctionNode';
import { NodeCategory, SocketDirection, SocketType } from '../nodes/Node';
import { NodeRegistry } from '../nodes/NodeRegistry';
import { ExecutionContext } from '../execution/ExecutionContext';

/**
 * 断点信息接口
 */
export interface BreakpointInfo {
  id: string;
  file: string;
  line: number;
  column?: number;
  condition?: string;
  enabled: boolean;
  hitCount: number;
  logMessage?: string;
}

/**
 * 调试会话接口
 */
export interface DebugSession {
  id: string;
  target: string;
  state: 'running' | 'paused' | 'stopped';
  breakpoints: BreakpointInfo[];
  callStack: any[];
  variables: Record<string, any>;
  watchExpressions: string[];
}

/**
 * 断点管理节点
 */
export class BreakpointManagerNode extends AsyncNode {
  private breakpoints: Map<string, BreakpointInfo> = new Map();

  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'addBreakpoint',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '添加断点'
    });

    this.addInput({
      name: 'removeBreakpoint',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '移除断点'
    });

    this.addInput({
      name: 'toggleBreakpoint',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '切换断点状态'
    });

    this.addInput({
      name: 'clearAll',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '清除所有断点'
    });

    this.addInput({
      name: 'file',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '文件路径',
      required: true
    });

    this.addInput({
      name: 'line',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '行号',
      required: true
    });

    this.addInput({
      name: 'column',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '列号'
    });

    this.addInput({
      name: 'condition',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '断点条件'
    });

    this.addInput({
      name: 'logMessage',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '日志消息'
    });

    this.addInput({
      name: 'breakpointId',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '断点ID'
    });

    // 输出插槽
    this.addOutput({
      name: 'onAdd',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '断点已添加'
    });

    this.addOutput({
      name: 'onRemove',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '断点已移除'
    });

    this.addOutput({
      name: 'onToggle',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '断点状态已切换'
    });

    this.addOutput({
      name: 'onClear',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '所有断点已清除'
    });

    this.addOutput({
      name: 'onHit',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '断点命中'
    });

    this.addOutput({
      name: 'breakpoint',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '当前操作的断点'
    });

    this.addOutput({
      name: 'breakpoints',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.OUTPUT,
      description: '所有断点列表'
    });

    this.addOutput({
      name: 'hitBreakpoint',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '命中的断点'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '错误信息'
    });
  }

  async execute(context: ExecutionContext): Promise<boolean> {
    const inputSocket = this.getTriggeredInputSocket();

    try {
      // 获取调试服务
      const debugService = context.world?.getService('Debugging');
      if (!debugService) {
        this.setOutputValue('error', '调试服务未找到');
        return false;
      }

      if (inputSocket?.name === 'addBreakpoint') {
        return this.addBreakpoint(debugService);
      } else if (inputSocket?.name === 'removeBreakpoint') {
        return this.removeBreakpoint(debugService);
      } else if (inputSocket?.name === 'toggleBreakpoint') {
        return this.toggleBreakpoint(debugService);
      } else if (inputSocket?.name === 'clearAll') {
        return this.clearAllBreakpoints(debugService);
      }

      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('断点管理操作失败:', errorMessage);
      this.setOutputValue('error', errorMessage);
      return false;
    }
  }

  /**
   * 添加断点
   */
  private async addBreakpoint(debugService: any): Promise<boolean> {
    const file = this.getInputValue('file') as string;
    const line = this.getInputValue('line') as number;
    const column = this.getInputValue('column') as number;
    const condition = this.getInputValue('condition') as string;
    const logMessage = this.getInputValue('logMessage') as string;

    if (!file || !line) {
      this.setOutputValue('error', '文件路径和行号不能为空');
      return false;
    }

    const breakpoint: BreakpointInfo = {
      id: this.generateBreakpointId(),
      file,
      line,
      column,
      condition,
      enabled: true,
      hitCount: 0,
      logMessage
    };

    // 添加到调试服务
    await debugService.addBreakpoint(breakpoint);
    
    // 本地存储
    this.breakpoints.set(breakpoint.id, breakpoint);

    // 设置输出值
    this.setOutputValue('breakpoint', breakpoint);
    this.updateBreakpointsList();

    // 触发事件
    this.triggerFlow('onAdd');
    return true;
  }

  /**
   * 移除断点
   */
  private async removeBreakpoint(debugService: any): Promise<boolean> {
    const breakpointId = this.getInputValue('breakpointId') as string;
    const file = this.getInputValue('file') as string;
    const line = this.getInputValue('line') as number;

    let targetId = breakpointId;
    if (!targetId && file && line) {
      // 根据文件和行号查找断点
      for (const [id, bp] of this.breakpoints.entries()) {
        if (bp.file === file && bp.line === line) {
          targetId = id;
          break;
        }
      }
    }

    if (!targetId) {
      this.setOutputValue('error', '未找到要移除的断点');
      return false;
    }

    const breakpoint = this.breakpoints.get(targetId);
    if (!breakpoint) {
      this.setOutputValue('error', '断点不存在');
      return false;
    }

    // 从调试服务移除
    await debugService.removeBreakpoint(targetId);
    
    // 本地移除
    this.breakpoints.delete(targetId);

    // 设置输出值
    this.setOutputValue('breakpoint', breakpoint);
    this.updateBreakpointsList();

    // 触发事件
    this.triggerFlow('onRemove');
    return true;
  }

  /**
   * 切换断点状态
   */
  private async toggleBreakpoint(debugService: any): Promise<boolean> {
    const breakpointId = this.getInputValue('breakpointId') as string;

    if (!breakpointId) {
      this.setOutputValue('error', '断点ID不能为空');
      return false;
    }

    const breakpoint = this.breakpoints.get(breakpointId);
    if (!breakpoint) {
      this.setOutputValue('error', '断点不存在');
      return false;
    }

    // 切换状态
    breakpoint.enabled = !breakpoint.enabled;
    
    // 更新调试服务
    await debugService.updateBreakpoint(breakpoint);

    // 设置输出值
    this.setOutputValue('breakpoint', breakpoint);
    this.updateBreakpointsList();

    // 触发事件
    this.triggerFlow('onToggle');
    return true;
  }

  /**
   * 清除所有断点
   */
  private async clearAllBreakpoints(debugService: any): Promise<boolean> {
    // 从调试服务清除
    await debugService.clearAllBreakpoints();
    
    // 本地清除
    this.breakpoints.clear();

    // 更新输出
    this.updateBreakpointsList();

    // 触发事件
    this.triggerFlow('onClear');
    return true;
  }

  /**
   * 更新断点列表输出
   */
  private updateBreakpointsList(): void {
    const breakpointsList = Array.from(this.breakpoints.values());
    this.setOutputValue('breakpoints', breakpointsList);
  }

  /**
   * 生成断点ID
   */
  private generateBreakpointId(): string {
    return `bp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 断点命中处理
   */
  public onBreakpointHit(breakpointId: string, context: any): void {
    const breakpoint = this.breakpoints.get(breakpointId);
    if (breakpoint) {
      breakpoint.hitCount++;
      this.setOutputValue('hitBreakpoint', { ...breakpoint, context });
      this.triggerFlow('onHit');
    }
  }
}

/**
 * 调试会话管理节点
 */
export class DebugSessionManagerNode extends AsyncNode {
  private sessions: Map<string, DebugSession> = new Map();

  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'start',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '启动调试会话'
    });

    this.addInput({
      name: 'stop',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '停止调试会话'
    });

    this.addInput({
      name: 'pause',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '暂停执行'
    });

    this.addInput({
      name: 'resume',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '继续执行'
    });

    this.addInput({
      name: 'stepOver',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '单步跳过'
    });

    this.addInput({
      name: 'stepInto',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '单步进入'
    });

    this.addInput({
      name: 'stepOut',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '单步跳出'
    });

    this.addInput({
      name: 'target',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '调试目标',
      required: true
    });

    this.addInput({
      name: 'sessionId',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '会话ID'
    });

    this.addInput({
      name: 'configuration',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '调试配置',
      defaultValue: {}
    });

    // 输出插槽
    this.addOutput({
      name: 'onStart',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '会话已启动'
    });

    this.addOutput({
      name: 'onStop',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '会话已停止'
    });

    this.addOutput({
      name: 'onPause',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行已暂停'
    });

    this.addOutput({
      name: 'onResume',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行已继续'
    });

    this.addOutput({
      name: 'onStep',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '单步执行完成'
    });

    this.addOutput({
      name: 'session',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '当前调试会话'
    });

    this.addOutput({
      name: 'callStack',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.OUTPUT,
      description: '调用堆栈'
    });

    this.addOutput({
      name: 'variables',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '变量信息'
    });

    this.addOutput({
      name: 'isDebugging',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否正在调试'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '错误信息'
    });
  }

  async execute(context: ExecutionContext): Promise<boolean> {
    const inputSocket = this.getTriggeredInputSocket();

    try {
      // 获取调试服务
      const debugService = context.world?.getService('Debugging');
      if (!debugService) {
        this.setOutputValue('error', '调试服务未找到');
        return false;
      }

      if (inputSocket?.name === 'start') {
        return this.startSession(debugService);
      } else if (inputSocket?.name === 'stop') {
        return this.stopSession(debugService);
      } else if (inputSocket?.name === 'pause') {
        return this.pauseExecution(debugService);
      } else if (inputSocket?.name === 'resume') {
        return this.resumeExecution(debugService);
      } else if (inputSocket?.name === 'stepOver') {
        return this.stepOver(debugService);
      } else if (inputSocket?.name === 'stepInto') {
        return this.stepInto(debugService);
      } else if (inputSocket?.name === 'stepOut') {
        return this.stepOut(debugService);
      }

      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('调试会话操作失败:', errorMessage);
      this.setOutputValue('error', errorMessage);
      return false;
    }
  }

  /**
   * 启动调试会话
   */
  private async startSession(debugService: any): Promise<boolean> {
    const target = this.getInputValue('target') as string;
    const configuration = this.getInputValue('configuration') as any;

    if (!target) {
      this.setOutputValue('error', '调试目标不能为空');
      return false;
    }

    const session: DebugSession = {
      id: this.generateSessionId(),
      target,
      state: 'running',
      breakpoints: [],
      callStack: [],
      variables: {},
      watchExpressions: []
    };

    // 启动调试会话
    await debugService.startSession(session, configuration);

    // 本地存储
    this.sessions.set(session.id, session);

    // 设置输出值
    this.setOutputValue('session', session);
    this.setOutputValue('isDebugging', true);
    this.updateSessionInfo(session);

    // 触发事件
    this.triggerFlow('onStart');
    return true;
  }

  /**
   * 停止调试会话
   */
  private async stopSession(debugService: any): Promise<boolean> {
    const sessionId = this.getInputValue('sessionId') as string || this.getCurrentSessionId();

    if (!sessionId) {
      this.setOutputValue('error', '会话ID不能为空');
      return false;
    }

    const session = this.sessions.get(sessionId);
    if (!session) {
      this.setOutputValue('error', '调试会话不存在');
      return false;
    }

    // 停止调试会话
    await debugService.stopSession(sessionId);

    // 更新状态
    session.state = 'stopped';
    this.sessions.delete(sessionId);

    // 设置输出值
    this.setOutputValue('session', session);
    this.setOutputValue('isDebugging', false);

    // 触发事件
    this.triggerFlow('onStop');
    return true;
  }

  /**
   * 暂停执行
   */
  private async pauseExecution(debugService: any): Promise<boolean> {
    const sessionId = this.getInputValue('sessionId') as string || this.getCurrentSessionId();

    if (!sessionId) {
      this.setOutputValue('error', '会话ID不能为空');
      return false;
    }

    const session = this.sessions.get(sessionId);
    if (!session) {
      this.setOutputValue('error', '调试会话不存在');
      return false;
    }

    // 暂停执行
    await debugService.pauseExecution(sessionId);

    // 更新状态
    session.state = 'paused';
    this.updateSessionInfo(session);

    // 触发事件
    this.triggerFlow('onPause');
    return true;
  }

  /**
   * 继续执行
   */
  private async resumeExecution(debugService: any): Promise<boolean> {
    const sessionId = this.getInputValue('sessionId') as string || this.getCurrentSessionId();

    if (!sessionId) {
      this.setOutputValue('error', '会话ID不能为空');
      return false;
    }

    const session = this.sessions.get(sessionId);
    if (!session) {
      this.setOutputValue('error', '调试会话不存在');
      return false;
    }

    // 继续执行
    await debugService.resumeExecution(sessionId);

    // 更新状态
    session.state = 'running';
    this.updateSessionInfo(session);

    // 触发事件
    this.triggerFlow('onResume');
    return true;
  }

  /**
   * 单步跳过
   */
  private async stepOver(debugService: any): Promise<boolean> {
    const sessionId = this.getInputValue('sessionId') as string || this.getCurrentSessionId();

    if (!sessionId) {
      this.setOutputValue('error', '会话ID不能为空');
      return false;
    }

    // 执行单步跳过
    await debugService.stepOver(sessionId);

    // 更新会话信息
    const session = this.sessions.get(sessionId);
    if (session) {
      this.updateSessionInfo(session);
    }

    // 触发事件
    this.triggerFlow('onStep');
    return true;
  }

  /**
   * 单步进入
   */
  private async stepInto(debugService: any): Promise<boolean> {
    const sessionId = this.getInputValue('sessionId') as string || this.getCurrentSessionId();

    if (!sessionId) {
      this.setOutputValue('error', '会话ID不能为空');
      return false;
    }

    // 执行单步进入
    await debugService.stepInto(sessionId);

    // 更新会话信息
    const session = this.sessions.get(sessionId);
    if (session) {
      this.updateSessionInfo(session);
    }

    // 触发事件
    this.triggerFlow('onStep');
    return true;
  }

  /**
   * 单步跳出
   */
  private async stepOut(debugService: any): Promise<boolean> {
    const sessionId = this.getInputValue('sessionId') as string || this.getCurrentSessionId();

    if (!sessionId) {
      this.setOutputValue('error', '会话ID不能为空');
      return false;
    }

    // 执行单步跳出
    await debugService.stepOut(sessionId);

    // 更新会话信息
    const session = this.sessions.get(sessionId);
    if (session) {
      this.updateSessionInfo(session);
    }

    // 触发事件
    this.triggerFlow('onStep');
    return true;
  }

  /**
   * 更新会话信息
   */
  private updateSessionInfo(session: DebugSession): void {
    this.setOutputValue('callStack', session.callStack);
    this.setOutputValue('variables', session.variables);
  }

  /**
   * 获取当前会话ID
   */
  private getCurrentSessionId(): string | null {
    const sessions = Array.from(this.sessions.values());
    const activeSession = sessions.find(s => s.state !== 'stopped');
    return activeSession?.id || null;
  }

  /**
   * 生成会话ID
   */
  private generateSessionId(): string {
    return `debug_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

/**
 * 变量监视节点
 */
export class VariableWatchNode extends AsyncNode {
  private watchExpressions: Map<string, any> = new Map();

  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'addWatch',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '添加监视表达式'
    });

    this.addInput({
      name: 'removeWatch',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '移除监视表达式'
    });

    this.addInput({
      name: 'updateWatch',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '更新监视值'
    });

    this.addInput({
      name: 'clearAll',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '清除所有监视'
    });

    this.addInput({
      name: 'expression',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '监视表达式',
      required: true
    });

    this.addInput({
      name: 'watchId',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '监视ID'
    });

    this.addInput({
      name: 'sessionId',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '调试会话ID'
    });

    // 输出插槽
    this.addOutput({
      name: 'onAdd',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '监视已添加'
    });

    this.addOutput({
      name: 'onRemove',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '监视已移除'
    });

    this.addOutput({
      name: 'onUpdate',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '监视已更新'
    });

    this.addOutput({
      name: 'onClear',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '所有监视已清除'
    });

    this.addOutput({
      name: 'onValueChange',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '监视值已变化'
    });

    this.addOutput({
      name: 'watchInfo',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '当前监视信息'
    });

    this.addOutput({
      name: 'watchList',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.OUTPUT,
      description: '所有监视列表'
    });

    this.addOutput({
      name: 'value',
      type: SocketType.DATA,
      dataType: 'any',
      direction: SocketDirection.OUTPUT,
      description: '监视值'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '错误信息'
    });
  }

  async execute(context: ExecutionContext): Promise<boolean> {
    const inputSocket = this.getTriggeredInputSocket();

    try {
      // 获取调试服务
      const debugService = context.world?.getService('Debugging');
      if (!debugService) {
        this.setOutputValue('error', '调试服务未找到');
        return false;
      }

      if (inputSocket?.name === 'addWatch') {
        return this.addWatch(debugService);
      } else if (inputSocket?.name === 'removeWatch') {
        return this.removeWatch(debugService);
      } else if (inputSocket?.name === 'updateWatch') {
        return this.updateWatch(debugService);
      } else if (inputSocket?.name === 'clearAll') {
        return this.clearAllWatches(debugService);
      }

      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('变量监视操作失败:', errorMessage);
      this.setOutputValue('error', errorMessage);
      return false;
    }
  }

  /**
   * 添加监视表达式
   */
  private async addWatch(debugService: any): Promise<boolean> {
    const expression = this.getInputValue('expression') as string;
    const sessionId = this.getInputValue('sessionId') as string;

    if (!expression) {
      this.setOutputValue('error', '监视表达式不能为空');
      return false;
    }

    const watchId = this.generateWatchId();
    const watchInfo = {
      id: watchId,
      expression,
      value: null,
      type: 'unknown',
      error: null,
      timestamp: Date.now()
    };

    // 添加到调试服务
    if (sessionId) {
      const value = await debugService.evaluateExpression(sessionId, expression);
      watchInfo.value = value.result;
      watchInfo.type = value.type;
      watchInfo.error = value.error;
    }

    // 本地存储
    this.watchExpressions.set(watchId, watchInfo);

    // 设置输出值
    this.setOutputValue('watchInfo', watchInfo);
    this.setOutputValue('value', watchInfo.value);
    this.updateWatchList();

    // 触发事件
    this.triggerFlow('onAdd');
    return true;
  }

  /**
   * 移除监视表达式
   */
  private async removeWatch(debugService: any): Promise<boolean> {
    const watchId = this.getInputValue('watchId') as string;
    const expression = this.getInputValue('expression') as string;

    let targetId = watchId;
    if (!targetId && expression) {
      // 根据表达式查找监视
      for (const [id, watch] of this.watchExpressions.entries()) {
        if (watch.expression === expression) {
          targetId = id;
          break;
        }
      }
    }

    if (!targetId) {
      this.setOutputValue('error', '未找到要移除的监视');
      return false;
    }

    const watchInfo = this.watchExpressions.get(targetId);
    if (!watchInfo) {
      this.setOutputValue('error', '监视不存在');
      return false;
    }

    // 本地移除
    this.watchExpressions.delete(targetId);

    // 设置输出值
    this.setOutputValue('watchInfo', watchInfo);
    this.updateWatchList();

    // 触发事件
    this.triggerFlow('onRemove');
    return true;
  }

  /**
   * 更新监视值
   */
  private async updateWatch(debugService: any): Promise<boolean> {
    const sessionId = this.getInputValue('sessionId') as string;
    const watchId = this.getInputValue('watchId') as string;

    if (!sessionId) {
      this.setOutputValue('error', '调试会话ID不能为空');
      return false;
    }

    const watchesToUpdate = watchId ?
      [this.watchExpressions.get(watchId)].filter(Boolean) :
      Array.from(this.watchExpressions.values());

    for (const watchInfo of watchesToUpdate) {
      try {
        const result = await debugService.evaluateExpression(sessionId, watchInfo.expression);
        const oldValue = watchInfo.value;

        watchInfo.value = result.result;
        watchInfo.type = result.type;
        watchInfo.error = result.error;
        watchInfo.timestamp = Date.now();

        // 检查值是否变化
        if (JSON.stringify(oldValue) !== JSON.stringify(watchInfo.value)) {
          this.setOutputValue('watchInfo', watchInfo);
          this.setOutputValue('value', watchInfo.value);
          this.triggerFlow('onValueChange');
        }
      } catch (error) {
        watchInfo.error = error instanceof Error ? error.message : String(error);
      }
    }

    this.updateWatchList();
    this.triggerFlow('onUpdate');
    return true;
  }

  /**
   * 清除所有监视
   */
  private async clearAllWatches(debugService: any): Promise<boolean> {
    this.watchExpressions.clear();
    this.updateWatchList();
    this.triggerFlow('onClear');
    return true;
  }

  /**
   * 更新监视列表输出
   */
  private updateWatchList(): void {
    const watchList = Array.from(this.watchExpressions.values());
    this.setOutputValue('watchList', watchList);
  }

  /**
   * 生成监视ID
   */
  private generateWatchId(): string {
    return `watch_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

/**
 * 注册高级调试节点
 * @param registry 节点注册表
 */
export function registerAdvancedDebuggingNodes(registry: NodeRegistry): void {
  // 注册断点管理节点
  registry.registerNodeType({
    type: 'debug/breakpoint-manager',
    category: NodeCategory.UTILITY,
    constructor: BreakpointManagerNode,
    label: '断点管理',
    description: '管理调试断点的添加、移除和状态',
    icon: 'bug',
    color: '#f5222d',
    tags: ['debug', 'breakpoint', 'debugging', 'development']
  });

  // 注册调试会话管理节点
  registry.registerNodeType({
    type: 'debug/session-manager',
    category: NodeCategory.UTILITY,
    constructor: DebugSessionManagerNode,
    label: '调试会话管理',
    description: '管理调试会话的启动、停止和控制',
    icon: 'play-circle',
    color: '#f5222d',
    tags: ['debug', 'session', 'debugging', 'control']
  });

  // 注册变量监视节点
  registry.registerNodeType({
    type: 'debug/variable-watch',
    category: NodeCategory.UTILITY,
    constructor: VariableWatchNode,
    label: '变量监视',
    description: '监视变量和表达式的值变化',
    icon: 'eye',
    color: '#f5222d',
    tags: ['debug', 'watch', 'variables', 'monitoring']
  });

  console.log('已注册所有高级调试节点类型');
}
