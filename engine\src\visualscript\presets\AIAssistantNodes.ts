/**
 * 视觉脚本AI辅助编程节点
 * 提供智能编程辅助和代码生成功能
 */
import { AsyncNode } from '../nodes/AsyncNode';
import { FunctionNode } from '../nodes/FunctionNode';
import { NodeCategory, SocketDirection, SocketType } from '../nodes/Node';
import { NodeRegistry } from '../nodes/NodeRegistry';
import { ExecutionContext } from '../execution/ExecutionContext';

/**
 * 代码建议接口
 */
export interface CodeSuggestion {
  id: string;
  type: 'completion' | 'refactor' | 'optimization' | 'fix';
  title: string;
  description: string;
  code: string;
  confidence: number;
  category: string;
  metadata?: Record<string, any>;
}

/**
 * AI代码补全节点
 */
export class AICodeCompletionNode extends AsyncNode {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    this.addInput({
      name: 'code',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '当前代码',
      required: true
    });

    this.addInput({
      name: 'cursorPosition',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '光标位置 {line, column}',
      required: true
    });

    this.addInput({
      name: 'language',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '编程语言',
      defaultValue: 'javascript'
    });

    this.addInput({
      name: 'context',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '上下文信息',
      defaultValue: {}
    });

    this.addInput({
      name: 'maxSuggestions',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '最大建议数量',
      defaultValue: 5
    });

    this.addInput({
      name: 'includeSnippets',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '是否包含代码片段',
      defaultValue: true
    });

    // 输出插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '补全成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '补全失败'
    });

    this.addOutput({
      name: 'suggestions',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.OUTPUT,
      description: '代码建议列表'
    });

    this.addOutput({
      name: 'bestSuggestion',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '最佳建议'
    });

    this.addOutput({
      name: 'completionTime',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '补全耗时（毫秒）'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '错误信息'
    });
  }

  async execute(context: ExecutionContext): Promise<boolean> {
    const code = this.getInputValue('code') as string;
    const cursorPosition = this.getInputValue('cursorPosition') as any;
    const language = this.getInputValue('language') as string;
    const contextInfo = this.getInputValue('context') as any;
    const maxSuggestions = this.getInputValue('maxSuggestions') as number;
    const includeSnippets = this.getInputValue('includeSnippets') as boolean;

    if (!code || !cursorPosition) {
      this.setOutputValue('error', '代码和光标位置不能为空');
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 获取AI辅助服务
      const aiService = context.world?.getService('AIAssistant');
      if (!aiService) {
        this.setOutputValue('error', 'AI辅助服务未找到');
        this.triggerFlow('fail');
        return false;
      }

      const startTime = Date.now();

      // 获取代码补全建议
      const suggestions = await (aiService as any).getCodeCompletion({
        code,
        cursorPosition,
        language,
        context: contextInfo,
        maxSuggestions,
        includeSnippets
      });

      const completionTime = Date.now() - startTime;

      // 找到最佳建议
      const bestSuggestion = suggestions.length > 0 ? 
        suggestions.reduce((best: any, current: any) => 
          current.confidence > best.confidence ? current : best
        ) : null;

      // 设置输出值
      this.setOutputValue('suggestions', suggestions);
      this.setOutputValue('bestSuggestion', bestSuggestion);
      this.setOutputValue('completionTime', completionTime);

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('AI代码补全失败:', errorMessage);
      this.setOutputValue('error', errorMessage);
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 代码重构建议节点
 */
export class CodeRefactorSuggestionNode extends AsyncNode {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    this.addInput({
      name: 'code',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '要重构的代码',
      required: true
    });

    this.addInput({
      name: 'selectedRange',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '选中范围 {start, end}'
    });

    this.addInput({
      name: 'refactorType',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '重构类型（extract_method, rename, inline, move）',
      defaultValue: 'auto'
    });

    this.addInput({
      name: 'language',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '编程语言',
      defaultValue: 'javascript'
    });

    this.addInput({
      name: 'codeStyle',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '代码风格配置',
      defaultValue: {}
    });

    // 输出插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '分析成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '分析失败'
    });

    this.addOutput({
      name: 'refactorSuggestions',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.OUTPUT,
      description: '重构建议列表'
    });

    this.addOutput({
      name: 'codeIssues',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.OUTPUT,
      description: '代码问题列表'
    });

    this.addOutput({
      name: 'complexity',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '代码复杂度分析'
    });

    this.addOutput({
      name: 'qualityScore',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '代码质量评分'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '错误信息'
    });
  }

  async execute(context: ExecutionContext): Promise<boolean> {
    const code = this.getInputValue('code') as string;
    const selectedRange = this.getInputValue('selectedRange') as any;
    const refactorType = this.getInputValue('refactorType') as string;
    const language = this.getInputValue('language') as string;
    const codeStyle = this.getInputValue('codeStyle') as any;

    if (!code) {
      this.setOutputValue('error', '代码不能为空');
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 获取AI辅助服务
      const aiService = context.world?.getService('AIAssistant');
      if (!aiService) {
        this.setOutputValue('error', 'AI辅助服务未找到');
        this.triggerFlow('fail');
        return false;
      }

      // 分析代码并获取重构建议
      const analysis = await (aiService as any).analyzeCodeForRefactoring({
        code,
        selectedRange,
        refactorType,
        language,
        codeStyle
      });

      // 设置输出值
      this.setOutputValue('refactorSuggestions', analysis.suggestions || []);
      this.setOutputValue('codeIssues', analysis.issues || []);
      this.setOutputValue('complexity', analysis.complexity || {});
      this.setOutputValue('qualityScore', analysis.qualityScore || 0);

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('代码重构分析失败:', errorMessage);
      this.setOutputValue('error', errorMessage);
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 智能代码生成节点
 */
export class SmartCodeGenerationNode extends AsyncNode {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    this.addInput({
      name: 'description',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '功能描述',
      required: true
    });

    this.addInput({
      name: 'language',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '目标编程语言',
      defaultValue: 'javascript'
    });

    this.addInput({
      name: 'framework',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '使用的框架'
    });

    this.addInput({
      name: 'codeStyle',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '代码风格偏好',
      defaultValue: {}
    });

    this.addInput({
      name: 'includeTests',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '是否包含测试代码',
      defaultValue: false
    });

    this.addInput({
      name: 'includeComments',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '是否包含注释',
      defaultValue: true
    });

    // 输出插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '生成成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '生成失败'
    });

    this.addOutput({
      name: 'generatedCode',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '生成的代码'
    });

    this.addOutput({
      name: 'testCode',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '生成的测试代码'
    });

    this.addOutput({
      name: 'documentation',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '生成的文档'
    });

    this.addOutput({
      name: 'confidence',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '生成置信度'
    });

    this.addOutput({
      name: 'generationTime',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '生成耗时（毫秒）'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '错误信息'
    });
  }

  async execute(context: ExecutionContext): Promise<boolean> {
    const description = this.getInputValue('description') as string;
    const language = this.getInputValue('language') as string;
    const framework = this.getInputValue('framework') as string;
    const codeStyle = this.getInputValue('codeStyle') as any;
    const includeTests = this.getInputValue('includeTests') as boolean;
    const includeComments = this.getInputValue('includeComments') as boolean;

    if (!description) {
      this.setOutputValue('error', '功能描述不能为空');
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 获取AI辅助服务
      const aiService = context.world?.getService('AIAssistant');
      if (!aiService) {
        this.setOutputValue('error', 'AI辅助服务未找到');
        this.triggerFlow('fail');
        return false;
      }

      const startTime = Date.now();

      // 生成代码
      const result = await (aiService as any).generateCode({
        description,
        language,
        framework,
        codeStyle,
        includeTests,
        includeComments
      });

      const generationTime = Date.now() - startTime;

      // 设置输出值
      this.setOutputValue('generatedCode', result.code || '');
      this.setOutputValue('testCode', result.testCode || '');
      this.setOutputValue('documentation', result.documentation || '');
      this.setOutputValue('confidence', result.confidence || 0);
      this.setOutputValue('generationTime', generationTime);

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('智能代码生成失败:', errorMessage);
      this.setOutputValue('error', errorMessage);
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 智能代码审查节点
 */
export class SmartCodeReviewNode extends AsyncNode {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    this.addInput({
      name: 'code',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '要审查的代码',
      required: true
    });

    this.addInput({
      name: 'reviewType',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '审查类型（security, performance, style, logic）',
      defaultValue: 'comprehensive'
    });

    this.addInput({
      name: 'language',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '编程语言',
      defaultValue: 'javascript'
    });

    this.addInput({
      name: 'severity',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '严重程度过滤（low, medium, high, critical）',
      defaultValue: 'medium'
    });

    this.addInput({
      name: 'includeFixSuggestions',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '是否包含修复建议',
      defaultValue: true
    });

    // 输出插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '审查成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '审查失败'
    });

    this.addOutput({
      name: 'reviewResult',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '审查结果'
    });

    this.addOutput({
      name: 'issues',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.OUTPUT,
      description: '发现的问题列表'
    });

    this.addOutput({
      name: 'securityIssues',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.OUTPUT,
      description: '安全问题'
    });

    this.addOutput({
      name: 'performanceIssues',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.OUTPUT,
      description: '性能问题'
    });

    this.addOutput({
      name: 'styleIssues',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.OUTPUT,
      description: '代码风格问题'
    });

    this.addOutput({
      name: 'overallScore',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '总体评分'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '错误信息'
    });
  }

  async execute(context: ExecutionContext): Promise<boolean> {
    const code = this.getInputValue('code') as string;
    const reviewType = this.getInputValue('reviewType') as string;
    const language = this.getInputValue('language') as string;
    const severity = this.getInputValue('severity') as string;
    const includeFixSuggestions = this.getInputValue('includeFixSuggestions') as boolean;

    if (!code) {
      this.setOutputValue('error', '代码不能为空');
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 获取AI辅助服务
      const aiService = context.world?.getService('AIAssistant');
      if (!aiService) {
        this.setOutputValue('error', 'AI辅助服务未找到');
        this.triggerFlow('fail');
        return false;
      }

      // 执行代码审查
      const reviewResult = await (aiService as any).reviewCode({
        code,
        reviewType,
        language,
        severity,
        includeFixSuggestions
      });

      // 分类问题
      const securityIssues = reviewResult.issues?.filter((issue: any) => issue.category === 'security') || [];
      const performanceIssues = reviewResult.issues?.filter((issue: any) => issue.category === 'performance') || [];
      const styleIssues = reviewResult.issues?.filter((issue: any) => issue.category === 'style') || [];

      // 设置输出值
      this.setOutputValue('reviewResult', reviewResult);
      this.setOutputValue('issues', reviewResult.issues || []);
      this.setOutputValue('securityIssues', securityIssues);
      this.setOutputValue('performanceIssues', performanceIssues);
      this.setOutputValue('styleIssues', styleIssues);
      this.setOutputValue('overallScore', reviewResult.overallScore || 0);

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('智能代码审查失败:', errorMessage);
      this.setOutputValue('error', errorMessage);
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 注册AI辅助编程节点
 * @param registry 节点注册表
 */
export function registerAIAssistantNodes(registry: NodeRegistry): void {
  // 注册AI代码补全节点
  registry.registerNodeType({
    type: 'ai/code-completion',
    category: NodeCategory.UTILITY,
    constructor: AICodeCompletionNode,
    label: 'AI代码补全',
    description: '基于AI的智能代码补全和建议',
    icon: 'bulb',
    color: '#eb2f96',
    tags: ['ai', 'completion', 'intellisense', 'assistant']
  });

  // 注册代码重构建议节点
  registry.registerNodeType({
    type: 'ai/refactor-suggestion',
    category: NodeCategory.UTILITY,
    constructor: CodeRefactorSuggestionNode,
    label: '代码重构建议',
    description: '分析代码并提供重构建议',
    icon: 'tool',
    color: '#eb2f96',
    tags: ['ai', 'refactor', 'analysis', 'quality']
  });

  // 注册智能代码生成节点
  registry.registerNodeType({
    type: 'ai/code-generation',
    category: NodeCategory.UTILITY,
    constructor: SmartCodeGenerationNode,
    label: '智能代码生成',
    description: '根据描述自动生成代码',
    icon: 'code',
    color: '#eb2f96',
    tags: ['ai', 'generation', 'automation', 'productivity']
  });

  // 注册智能代码审查节点
  registry.registerNodeType({
    type: 'ai/code-review',
    category: NodeCategory.UTILITY,
    constructor: SmartCodeReviewNode,
    label: '智能代码审查',
    description: '自动审查代码质量和安全性',
    icon: 'audit',
    color: '#eb2f96',
    tags: ['ai', 'review', 'security', 'quality']
  });

  console.log('已注册所有AI辅助编程节点类型');
}
